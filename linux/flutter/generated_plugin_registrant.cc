//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <desktop_multi_window/desktop_multi_window_plugin.h>
#include <flutter_desktop_context_menu/flutter_desktop_context_menu_plugin.h>
#include <flutter_js/flutter_js_plugin.h>
#include <proxy_manager/proxy_manager_plugin.h>
#include <screen_retriever_linux/screen_retriever_linux_plugin.h>
#include <url_launcher_linux/url_launcher_plugin.h>
#include <window_manager/window_manager_plugin.h>
#include <zstandard_linux/zstandard_linux_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) desktop_multi_window_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "DesktopMultiWindowPlugin");
  desktop_multi_window_plugin_register_with_registrar(desktop_multi_window_registrar);
  g_autoptr(FlPluginRegistrar) flutter_desktop_context_menu_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "FlutterDesktopContextMenuPlugin");
  flutter_desktop_context_menu_plugin_register_with_registrar(flutter_desktop_context_menu_registrar);
  g_autoptr(FlPluginRegistrar) flutter_js_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "FlutterJsPlugin");
  flutter_js_plugin_register_with_registrar(flutter_js_registrar);
  g_autoptr(FlPluginRegistrar) proxy_manager_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "ProxyManagerPlugin");
  proxy_manager_plugin_register_with_registrar(proxy_manager_registrar);
  g_autoptr(FlPluginRegistrar) screen_retriever_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "ScreenRetrieverLinuxPlugin");
  screen_retriever_linux_plugin_register_with_registrar(screen_retriever_linux_registrar);
  g_autoptr(FlPluginRegistrar) url_launcher_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "UrlLauncherPlugin");
  url_launcher_plugin_register_with_registrar(url_launcher_linux_registrar);
  g_autoptr(FlPluginRegistrar) window_manager_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "WindowManagerPlugin");
  window_manager_plugin_register_with_registrar(window_manager_registrar);
  g_autoptr(FlPluginRegistrar) zstandard_linux_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "ZstandardLinuxPlugin");
  zstandard_linux_plugin_register_with_registrar(zstandard_linux_registrar);
}
