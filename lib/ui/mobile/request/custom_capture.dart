/*
 * Copyright 2023 <PERSON><PERSON> All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:convert';
import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:proxypin/ui/mobile/request/capture_filter_config.dart';
import 'package:proxypin/network/components/capture_filter.dart';
import 'package:proxypin/network/http/http.dart';
import 'package:proxypin/utils/listenable_list.dart';

/// 自定义捕获页面
/// 显示根据配置过滤器捕获到的请求列表
class CustomCapture extends StatefulWidget {
  const CustomCapture({super.key});

  @override
  State<CustomCapture> createState() => _CustomCaptureState();
}

class _CustomCaptureState extends State<CustomCapture> {
  // 过滤器配置相关
  List<CaptureFilter> _filters = [];

  // 捕获到的请求列表
  final ListenableList<HttpRequest> _capturedRequests = ListenableList<HttpRequest>();

  // IP上传响应结果映射 (requestId -> UploadResponseResult)
  final Map<String, UploadResponseResult> _uploadResponses = {};

  // 统计信息
  int _totalCaptured = 0;
  DateTime? _lastCaptureTime;

  // SharedPreferences key
  static const String _keyFilters = 'custom_capture_filters';

  // 默认过滤器配置
  static final List<CaptureFilter> _defaultFilters = [
    CaptureFilter(
      name: 'test',
      type: CaptureFilterType.ipUpload,
      value: '',
      enabled: true,
      extractObject: '',
      submitUrl: '',
      submitVariables: [
        SubmitVariable(name: 'token', defaultValue: '222222'),
        SubmitVariable(name: 'type', defaultValue: 'Mini_programs'),
      ],
    ),

  ];

  @override
  void initState() {
    super.initState();
    // 监听捕获到的请求
    _setupCaptureListener();
    // 加载保存的过滤器
    _loadSavedFilters();
  }

  @override
  void dispose() {
    _capturedRequests.clear();
    super.dispose();
  }

  void _setupCaptureListener() {
    // 设置拦截器的回调，当捕获到请求时添加到列表
    CaptureFilterInterceptor.instance.onRequestCaptured = (request) {
      setState(() {
        _capturedRequests.add(request);
        _totalCaptured++;
        _lastCaptureTime = DateTime.now();
      });
    };

    // 设置IP上传响应回调
    CaptureFilterInterceptor.instance.onUploadResponse = (uploadResult) {
      print('📥 收到上传响应回调:');
      print('   - 请求URL: ${uploadResult.requestUrl}');
      print('   - 成功状态: ${uploadResult.success}');
      print('   - 过滤器名: ${uploadResult.filterName}');
      print('   - 响应消息: ${uploadResult.message}');
      print('   - 提取数据: ${uploadResult.extractedData}');
      print('   - 服务器响应: ${uploadResult.serverResponse}');

      setState(() {
        // 使用请求URL作为key来关联响应结果
        _uploadResponses[uploadResult.requestUrl] = uploadResult;
        print('✅ 已存储上传响应，当前总数: ${_uploadResponses.length}');
        print('   - 存储键: ${uploadResult.requestUrl}');
        print('   - 所有键: ${_uploadResponses.keys.toList()}');
      });
    };
  }

  Future<void> _loadSavedFilters() async {
    final prefs = await SharedPreferences.getInstance();
    final filtersJson = prefs.getString(_keyFilters);

    if (filtersJson != null) {
      try {
        final List<dynamic> filtersList = json.decode(filtersJson);
        final List<CaptureFilter> loadedFilters = filtersList
            .map((filterData) => CaptureFilter.fromJson(filterData))
            .toList();

        setState(() {
          _filters = loadedFilters;
        });

        // 更新拦截器配置
        CaptureFilterInterceptor.instance.updateFilters(_filters);
      } catch (e) {
        print('加载过滤器失败: $e');
        // 加载失败时使用默认过滤器
        _loadDefaultFilters();
      }
    } else {
      // 没有保存的过滤器时使用默认过滤器
      _loadDefaultFilters();
    }
  }

  /// 加载默认过滤器
  void _loadDefaultFilters() {
    setState(() {
      _filters = _defaultFilters.map((filter) => filter.copyWith()).toList();
    });

    // 更新拦截器配置
    CaptureFilterInterceptor.instance.updateFilters(_filters);

    // 自动保存默认过滤器
    _saveFilters();
  }

  Future<void> _saveFilters() async {
    final prefs = await SharedPreferences.getInstance();
    final filtersJson = json.encode(_filters.map((f) => f.toJson()).toList());
    await prefs.setString(_keyFilters, filtersJson);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        title: const Text(
          '自定义',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          // 配置按钮
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: _openConfigPage,
              icon: const Icon(Icons.tune, size: 18),
              label: const Text('配置'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ),
          // 清空按钮
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: ElevatedButton.icon(
              onPressed: _clearCapturedRequests,
              icon: const Icon(Icons.clear_all, size: 18),
              label: const Text('清空'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // JSON响应横条
          _buildJsonResponseBar(),
          // 统计信息栏
          _buildStatsBar(),
          // 过滤器状态栏
          _buildFilterStatusBar(),
          // 请求列表
          Expanded(
            child: _buildRequestList(),
          ),
        ],
      ),
    );
  }





  /// 构建JSON响应横条
  Widget _buildJsonResponseBar() {
    // 获取最新的JSON响应
    String? latestJsonResponse = _getLatestJsonResponse();

    if (latestJsonResponse == null || latestJsonResponse.isEmpty) {
      return const SizedBox.shrink(); // 如果没有JSON响应，不显示
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200, width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 18,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              latestJsonResponse,
              style: TextStyle(
                color: Colors.red.shade800,
                fontSize: 13,
                fontFamily: 'monospace',
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取最新的JSON响应
  String? _getLatestJsonResponse() {
    if (_capturedRequests.isEmpty) return null;

    // 查找最新的有JSON响应的请求
    for (var request in _capturedRequests.toList().reversed) {
      if (request.response?.bodyAsString != null &&
          request.response!.bodyAsString!.isNotEmpty) {
        try {
          // 尝试解析JSON以确认是有效的JSON
          json.decode(request.response!.bodyAsString!);
          return request.response!.bodyAsString!;
        } catch (e) {
          // 如果不是有效JSON，继续查找
          continue;
        }
      }
    }
    return null;
  }
  Widget _buildStatsBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.withOpacity(0.3))),
      ),
      child: Row(
        children: [
          Icon(Icons.analytics, color: Colors.blue),
          const SizedBox(width: 8),
          Text(
            '已捕获: $_totalCaptured',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 16),
          Text(
            '当前显示: ${_capturedRequests.length}',
            style: const TextStyle(color: Colors.grey),
          ),
          const Spacer(),
          if (_lastCaptureTime != null)
            Text(
              '最后捕获: ${_formatTime(_lastCaptureTime!)}',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterStatusBar() {
    final enabledFilters = _filters.where((f) => f.enabled).length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: enabledFilters > 0 ? Colors.green.withOpacity(0.1) : Colors.orange.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.withOpacity(0.3))),
      ),
      child: Row(
        children: [
          Icon(
            enabledFilters > 0 ? Icons.filter_alt : Icons.filter_alt_off,
            color: enabledFilters > 0 ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            enabledFilters > 0
                ? '过滤器已启用 ($enabledFilters/${_filters.length})'
                : '未配置过滤器',
            style: TextStyle(
              color: enabledFilters > 0 ? Colors.green : Colors.orange,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: _openConfigPage,
            icon: const Icon(Icons.settings, size: 16),
            label: const Text('配置'),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestList() {
    if (_capturedRequests.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '暂无捕获的请求',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '配置过滤器后，匹配的请求将显示在这里',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _capturedRequests.length,
      itemBuilder: (context, index) {
        final request = _capturedRequests.elementAt(index);
        return _buildRequestItem(request, index);
      },
    );
  }

  Widget _buildRequestItem(HttpRequest request, int index) {
    final statusCode = request.response?.status.code ?? 0;
    final method = request.method.name;
    final url = request.requestUrl;
    final time = DateTime.now(); // 实际应该使用请求时间

    // 获取对应的上传响应
    final uploadResponse = _uploadResponses[url];

    // 详细调试信息
    print('🔍 构建请求项调试:');
    print('   - 请求URL: $url');
    print('   - 有上传响应: ${uploadResponse != null}');
    print('   - 总上传响应数: ${_uploadResponses.length}');
    print('   - 上传响应键列表: ${_uploadResponses.keys.toList()}');
    if (uploadResponse != null) {
      print('   - 响应过滤器: ${uploadResponse.filterName}');
      print('   - 响应成功: ${uploadResponse.success}');
      print('   - 响应消息: ${uploadResponse.message}');
    }

    Color statusColor = Colors.grey;
    if (statusCode >= 200 && statusCode < 300) {
      statusColor = Colors.green;
    } else if (statusCode >= 300 && statusCode < 400) {
      statusColor = Colors.orange;
    } else if (statusCode >= 400) {
      statusColor = Colors.red;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            // 请求信息部分
            ListTile(
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              leading: CircleAvatar(
                backgroundColor: statusColor,
                radius: 16,
                child: Text(
                  statusCode.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              title: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getMethodColor(method),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      method,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      url,
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '时间: ${_formatTime(time)}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  if (request.response?.headers.contentType != null)
                    Text(
                      '类型: ${request.response!.headers.contentType}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                ],
              ),
              trailing: IconButton(
                icon: const Icon(Icons.info_outline),
                onPressed: () => _showRequestDetails(request),
              ),
              onTap: () => _showRequestDetails(request),
            ),

            // 如果有上传响应，显示连接的响应区域
            if (uploadResponse != null) ...[
              // 优雅的分隔线
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                height: 1,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      uploadResponse.success
                          ? Colors.green.shade300.withOpacity(0.5)
                          : Colors.red.shade300.withOpacity(0.5),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
              // 响应内容区域
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 8),
                decoration: BoxDecoration(
                  color: uploadResponse.success
                      ? Colors.green.shade50.withOpacity(0.3)
                      : Colors.red.shade50.withOpacity(0.3),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 现代化状态标签
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: uploadResponse.success ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: (uploadResponse.success ? Colors.green : Colors.red).withOpacity(0.3),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  uploadResponse.success ? Icons.cloud_done_rounded : Icons.cloud_off_rounded,
                                  color: Colors.white,
                                  size: 16,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  uploadResponse.success ? '上传成功' : '上传失败',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              uploadResponse.filterName,
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade700,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // 现代化JSON响应容器
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: uploadResponse.success
                                ? Colors.green.shade200
                                : Colors.red.shade200,
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.05),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 响应头部
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: uploadResponse.success
                                    ? Colors.green.shade50
                                    : Colors.red.shade50,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(12),
                                  topRight: Radius.circular(12),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.data_object_rounded,
                                    size: 16,
                                    color: uploadResponse.success
                                        ? Colors.green.shade700
                                        : Colors.red.shade700,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '服务器响应',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: uploadResponse.success
                                          ? Colors.green.shade700
                                          : Colors.red.shade700,
                                    ),
                                  ),
                                  const Spacer(),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(0.8),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Text(
                                      'JSON',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // JSON内容区域
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(12),
                              child: SelectableText(
                                _formatJsonResponse(uploadResponse),
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontFamily: 'monospace',
                                  color: Colors.black87,
                                  height: 1.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getMethodColor(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return Colors.blue;
      case 'POST':
        return Colors.green;
      case 'PUT':
        return Colors.orange;
      case 'DELETE':
        return Colors.red;
      case 'PATCH':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _openConfigPage() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CaptureFilterConfig(
          currentFilters: _filters,
          onFiltersChanged: (filters) {
            setState(() {
              _filters = filters;
            });
            // 更新拦截器配置
            CaptureFilterInterceptor.instance.updateFilters(_filters);
            // 保存过滤器配置
            _saveFilters();
          },
        ),
      ),
    );
  }

  void _clearCapturedRequests() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认清空'),
        content: const Text('确定要清空所有捕获的请求吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _capturedRequests.clear();
                _uploadResponses.clear(); // 同时清空上传响应
                _totalCaptured = 0;
                _lastCaptureTime = null;
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('已清空所有数据')),
              );
            },
            child: const Text('清空', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showRequestDetails(HttpRequest request) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('请求详情'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('URL', request.requestUrl),
              _buildDetailRow('方法', request.method.name),
              _buildDetailRow('状态码', request.response?.status.code.toString() ?? '未知'),
              if (request.response?.headers.contentType != null)
                _buildDetailRow('内容类型', request.response!.headers.contentType!),
              if (request.response?.bodyAsString != null)
                _buildDetailRow('响应体长度', '${request.response!.bodyAsString!.length} 字符'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}';
  }

  /// 格式化JSON响应，无论成功失败都显示完整内容
  String _formatJsonResponse(UploadResponseResult uploadResponse) {
    try {
      // 优先显示服务器响应
      if (uploadResponse.serverResponse.isNotEmpty) {
        return const JsonEncoder.withIndent('  ').convert(uploadResponse.serverResponse);
      }

      // 如果没有服务器响应，显示消息
      if (uploadResponse.message.isNotEmpty) {
        // 尝试解析消息是否为JSON
        try {
          final messageJson = json.decode(uploadResponse.message);
          return const JsonEncoder.withIndent('  ').convert(messageJson);
        } catch (e) {
          // 如果不是JSON，直接返回消息
          return uploadResponse.message;
        }
      }

      // 如果都没有，返回默认信息
      return uploadResponse.success ? '上传成功，无响应内容' : '上传失败，无错误详情';
    } catch (e) {
      // 格式化失败时返回原始内容
      return uploadResponse.message.isNotEmpty
          ? uploadResponse.message
          : '响应格式化失败: $e';
    }
  }

  String _truncateUrl(String url) {
    if (url.length <= 30) return url;
    return '${url.substring(0, 27)}...';
  }

  String _formatJsonPreview(Map<String, dynamic> data) {
    try {
      final jsonString = const JsonEncoder.withIndent('  ').convert(data);
      final lines = jsonString.split('\n');
      if (lines.length <= 3) return jsonString;
      return '${lines.take(3).join('\n')}\n...';
    } catch (e) {
      return data.toString();
    }
  }

  Widget _buildSectionHeader(String title, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, size: 16, color: color),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoGrid(List<_InfoItem> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withOpacity(0.2)),
          ),
          child: Row(
            children: [
              Icon(item.icon, size: 14, color: Colors.grey[600]),
              const SizedBox(width: 6),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.label,
                      style: const TextStyle(
                        fontSize: 9,
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      item.value,
                      style: const TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }





  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 12),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }



  void _showUploadResponseDetails(UploadResponseResult response) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(response.filterName),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailSection('基本信息', [
                _buildDetailRow('时间', _formatTime(response.timestamp)),
                _buildDetailRow('状态', response.success ? '成功' : '失败'),
                _buildDetailRow('请求URL', response.requestUrl),
                _buildDetailRow('上传URL', response.uploadUrl),
              ]),
              const SizedBox(height: 16),
              _buildDetailSection('提取数据', [
                Text(
                  response.extractedData.toString(),
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ]),
              const SizedBox(height: 16),
              _buildDetailSection('服务器响应', [
                Text(
                  response.serverResponse.toString(),
                  style: const TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ],
    );
  }
}

/// 信息项
class _InfoItem {
  final String label;
  final String value;
  final IconData icon;

  const _InfoItem(this.label, this.value, this.icon);
}
