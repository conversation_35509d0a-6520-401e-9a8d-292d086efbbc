/*
 * Copyright 2023 <PERSON><PERSON> Wang All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:proxypin/network/components/capture_filter.dart';
import 'package:proxypin/ui/mobile/request/capture_filter_config.dart';

/// 任务获取页面
/// 从服务器拉取任务配置并自动转换为过滤器
class TaskFetch extends StatefulWidget {
  const TaskFetch({super.key});

  @override
  State<TaskFetch> createState() => _TaskFetchState();
}

class _TaskFetchState extends State<TaskFetch> {
  // 任务获取相关
  final _serverUrlController = TextEditingController();
  final _tokenController = TextEditingController();
  bool _isLoading = false;
  bool _goodsLink = false;
  List<TaskInfo> _tasks = [];
  List<dynamic> _rawTasks = []; // 存储原始任务数据
  DateTime? _lastFetchTime;

  // 功能开关配置
  static const bool _showServerUrlInput = false; // 设置为 true 显示服务器地址输入框
  static const String _defaultServerUrl = 'https://1.13.15.165:10113/get_PDDdata/'; // 默认服务器地址

  // SharedPreferences keys
  static const String _keyServerUrl = 'task_fetch_server_url';
  static const String _keyToken = 'task_fetch_token';
  static const String _keyGoodsLink = 'task_fetch_goods_link';
  static const String _keyRawTasks = 'task_fetch_raw_tasks';
  static const String _keyLastFetchTime = 'task_fetch_last_fetch_time';

  @override
  void initState() {
    super.initState();
    _loadSavedData();
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    _tokenController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedData() async {
    final prefs = await SharedPreferences.getInstance();

    // 加载保存的任务数据
    final rawTasksJson = prefs.getString(_keyRawTasks);
    final lastFetchTimeMs = prefs.getInt(_keyLastFetchTime);

    print('🔍 加载数据: rawTasksJson = $rawTasksJson');
    print('🔍 加载数据: lastFetchTimeMs = $lastFetchTimeMs');

    List<dynamic> savedRawTasks = [];
    List<TaskInfo> savedTasks = [];
    DateTime? savedLastFetchTime;

    if (rawTasksJson != null) {
      try {
        final decoded = json.decode(rawTasksJson);
        if (decoded is List) {
          savedRawTasks = decoded;
          print('🔍 成功解析任务数据，数量: ${savedRawTasks.length}');
          // 重新解析为TaskInfo对象
          for (var taskData in savedRawTasks) {
            try {
              savedTasks.add(TaskInfo.fromJson(taskData));
            } catch (e) {
              print('解析保存的任务数据失败: $e');
            }
          }
        }
      } catch (e) {
        print('解析保存的任务JSON失败: $e');
      }
    } else {
      print('🔍 没有找到保存的任务数据');
    }

    if (lastFetchTimeMs != null) {
      savedLastFetchTime = DateTime.fromMillisecondsSinceEpoch(lastFetchTimeMs);
    }

    setState(() {
      // 优先使用默认服务器地址，如果显示输入框则从SharedPreferences读取
      _serverUrlController.text = _showServerUrlInput
          ? (prefs.getString(_keyServerUrl) ?? _defaultServerUrl)
          : _defaultServerUrl;
      _tokenController.text = prefs.getString(_keyToken) ?? '';
      _goodsLink = prefs.getBool(_keyGoodsLink) ?? false;

      // 恢复任务数据
      _rawTasks = savedRawTasks;
      _tasks = savedTasks;
      _lastFetchTime = savedLastFetchTime;
    });
  }

  Future<void> _saveData() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(_keyServerUrl, _serverUrlController.text.trim());
    await prefs.setString(_keyToken, _tokenController.text.trim());
    await prefs.setBool(_keyGoodsLink, _goodsLink);

    // 保存任务数据
    print('💾 准备保存任务数据，数量: ${_rawTasks.length}');
    if (_rawTasks.isNotEmpty) {
      try {
        final rawTasksJson = json.encode(_rawTasks);
        await prefs.setString(_keyRawTasks, rawTasksJson);
        print('💾 任务数据保存成功');
      } catch (e) {
        print('保存任务数据失败: $e');
      }
    } else {
      // 如果没有任务数据，清除保存的数据
      await prefs.remove(_keyRawTasks);
      print('💾 清除了保存的任务数据');
    }

    // 保存最后获取时间
    if (_lastFetchTime != null) {
      await prefs.setInt(_keyLastFetchTime, _lastFetchTime!.millisecondsSinceEpoch);
    } else {
      await prefs.remove(_keyLastFetchTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('任务获取'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showTokenSettings,
            tooltip: '配置Token',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 获取任务按钮 - 居中加长
            Center(
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _fetchTasks,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                        )
                      : const Icon(Icons.download, size: 24),
                  label: Text(
                    _isLoading ? '获取中...' : '获取任务',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shadowColor: Colors.blue.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // 任务列表标题 - 固定不滚动
            Row(
              children: [
                const Icon(Icons.task_alt, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '任务列表',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (_tasks.isNotEmpty)
                      Text(
                        '共 ${_tasks.length} 个任务',
                        style: const TextStyle(color: Colors.grey),
                      ),
                    if (_lastFetchTime != null)
                      Text(
                        '最后获取: ${_formatTime(_lastFetchTime!)}',
                        style: const TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 任务内容区域 - 可滚动
            Expanded(
              child: _rawTasks.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.inbox, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            '暂无任务',
                            style: TextStyle(fontSize: 16, color: Colors.grey),
                          ),
                          SizedBox(height: 8),
                          Text(
                            '点击获取任务后将显示任务信息',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: _buildTaskInfoDisplay(_rawTasks.first),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}';
  }

  /// 显示Token设置对话框
  void _showTokenSettings() {
    final tokenController = TextEditingController(text: _tokenController.text);
    bool goodsLinkValue = _goodsLink;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('任务配置'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: tokenController,
                decoration: const InputDecoration(
                  labelText: 'Token',
                  hintText: '请输入Token',
                  prefixIcon: Icon(Icons.key),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<bool>(
                value: goodsLinkValue,
                decoration: const InputDecoration(
                  labelText: 'goodsLink',
                  prefixIcon: Icon(Icons.link),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: true,
                    child: Text('true'),
                  ),
                  DropdownMenuItem(
                    value: false,
                    child: Text('false'),
                  ),
                ],
                onChanged: (value) {
                  setDialogState(() {
                    goodsLinkValue = value ?? false;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _tokenController.text = tokenController.text;
                  _goodsLink = goodsLinkValue;
                });
                _saveData();
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('配置已保存')),
                );
              },
              child: const Text('保存'),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示详细的结果信息（成功或失败）
  void _showDetailedResult({
    required bool success,
    required String title,
    Map<String, dynamic>? data,
    int? taskCount,
    int? statusCode,
    String? responseBody,
    String? error,
    String? stackTrace,
    List<dynamic>? rawTaskList,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? Colors.green : Colors.red,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: success ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Container(
          width: double.maxFinite,
          constraints: const BoxConstraints(maxHeight: 500),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (success) ...[
                  // 成功信息
                  _buildInfoCard(
                    '获取结果',
                    [
                      _buildSelectableDetailRow('任务数量', '$taskCount 个'),
                      _buildSelectableDetailRow('获取时间', _formatTime(DateTime.now())),
                      _buildSelectableDetailRow('服务器地址', _serverUrlController.text.trim()),
                      _buildSelectableDetailRow('Token', _tokenController.text.trim()),
                    ],
                    Colors.green,
                  ),
                  const SizedBox(height: 16),

                  // 任务详情列表
                  if (rawTaskList != null && rawTaskList.isNotEmpty) ...[
                    _buildInfoCard(
                      '任务详情',
                      [
                        ...rawTaskList.asMap().entries.map((entry) {
                          final index = entry.key;
                          final task = entry.value;
                          return _buildTaskDetailCard(index + 1, task);
                        }).toList(),
                      ],
                      Colors.blue,
                    ),
                    const SizedBox(height: 16),
                  ],

                  if (data != null) ...[
                    _buildInfoCard(
                      '完整服务器响应',
                      [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: SelectableText(
                            const JsonEncoder.withIndent('  ').convert(data),
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                      Colors.grey,
                    ),
                  ],
                ] else ...[
                  // 失败信息
                  _buildInfoCard(
                    '错误详情',
                    [
                      if (statusCode != null)
                        _buildDetailRow('状态码', statusCode.toString()),
                      if (error != null)
                        _buildDetailRow('错误信息', error),
                      _buildDetailRow('请求时间', _formatTime(DateTime.now())),
                      _buildDetailRow('服务器地址', _serverUrlController.text.trim()),
                    ],
                    Colors.red,
                  ),
                  const SizedBox(height: 16),
                  if (responseBody != null && responseBody.isNotEmpty) ...[
                    _buildInfoCard(
                      '服务器响应',
                      [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: SelectableText(
                            _formatResponseBody(responseBody),
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                      Colors.orange,
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (stackTrace != null) ...[
                    _buildInfoCard(
                      '堆栈跟踪',
                      [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: SelectableText(
                            stackTrace,
                            style: const TextStyle(
                              fontSize: 10,
                              fontFamily: 'monospace',
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                      Colors.grey,
                    ),
                  ],
                ],
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          if (success && taskCount != null && taskCount > 0)
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // 可以添加跳转到任务列表的逻辑
              },
              child: const Text('查看任务'),
            ),
        ],
      ),
    );
  }

  /// 构建信息卡片
  Widget _buildInfoCard(String title, List<Widget> children, Color color) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  _getIconForTitle(title),
                  size: 16,
                  color: color,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// 根据标题获取图标
  IconData _getIconForTitle(String title) {
    switch (title) {
      case '获取结果':
        return Icons.task_alt;
      case '服务器响应':
        return Icons.data_object;
      case '错误详情':
        return Icons.error_outline;
      case '堆栈跟踪':
        return Icons.bug_report;
      default:
        return Icons.info_outline;
    }
  }

  /// 格式化响应体
  String _formatResponseBody(String responseBody) {
    try {
      // 尝试解析为JSON并格式化
      final data = json.decode(responseBody);
      return const JsonEncoder.withIndent('  ').convert(data);
    } catch (e) {
      // 如果不是JSON，直接返回原始内容
      return responseBody;
    }
  }

  /// 构建现代化的可选择详情行
  Widget _buildSelectableDetailRow(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.copy,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                onPressed: () => _copyToClipboard(value, label),
                tooltip: '复制$label',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 32,
                  minHeight: 32,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SelectableText(
            value,
            style: const TextStyle(
              fontSize: 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建任务详情卡片
  Widget _buildTaskDetailCard(int index, dynamic task) {
    if (task is! Map<String, dynamic>) {
      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Text('任务 $index: 数据格式错误'),
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 任务标题
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Text(
              '任务 $index',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(height: 12),

          // 主要字段
          if (task['goodsId'] != null)
            _buildSelectableDetailRow('商品ID', task['goodsId'].toString()),
          if (task['sourceItemName'] != null)
            _buildSelectableDetailRow('商品名称', task['sourceItemName'].toString()),
          if (task['sourceUrl'] != null)
            _buildSelectableDetailRow('商品链接', task['sourceUrl'].toString()),

          // 其他字段
          ...task.entries.where((entry) =>
            !['goodsId', 'sourceItemName', 'sourceUrl'].contains(entry.key)
          ).map((entry) =>
            _buildSelectableDetailRow(
              entry.key,
              entry.value?.toString() ?? 'null'
            )
          ).toList(),
        ],
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('已复制$label'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  /// 格式化字段名
  String _formatFieldName(String fieldName) {
    // 将驼峰命名转换为更友好的显示名称
    final Map<String, String> fieldNameMap = {
      'goodsId': '商品ID',
      'sourceItemName': '商品名称',
      'sourceUrl': '商品链接',
      'callbackData': '回调数据',
      'sourceShopName': '店铺名称',
      'sourceUrl': '源链接',
      'shortUrl': '短链接',
      // 可以根据需要添加更多映射
    };

    return fieldNameMap[fieldName] ?? fieldName;
  }

  /// 构建任务信息显示区域
  Widget _buildTaskInfoDisplay(dynamic task) {
    if (task is! Map<String, dynamic>) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.red.shade300),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade600, size: 24),
            const SizedBox(width: 12),
            const Text(
              '任务数据格式错误',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主要字段显示（商品链接放在第一位）
        if (task['sourceUrl'] != null)
          _buildTaskInfoField('商品链接', task['sourceUrl'].toString(), Icons.link),

        if (task['goodsId'] != null)
          _buildTaskInfoField('商品ID', task['goodsId'].toString(), Icons.tag),

        if (task['sourceItemName'] != null)
          _buildTaskInfoField('商品名称', task['sourceItemName'].toString(), Icons.shopping_cart),

        // 其他字段（排除不需要显示的字段）
        ...task.entries.where((entry) =>
          !['goodsId', 'sourceItemName', 'sourceUrl', 'callbackData', 'sourceShopName', 'shopId'].contains(entry.key)
        ).map((entry) =>
          _buildTaskInfoField(
            _formatFieldName(entry.key),
            entry.value?.toString() ?? 'null',
            _getFieldIcon(entry.key),
          )
        ).toList(),
      ],
    );
  }

  /// 构建任务信息字段
  Widget _buildTaskInfoField(String label, String value, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 字段标题栏
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: 20,
                  color: Colors.blue.shade600,
                ),
                const SizedBox(width: 12),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(
                    Icons.copy,
                    size: 18,
                    color: Colors.grey.shade600,
                  ),
                  onPressed: () => _copyToClipboard(value, label),
                  tooltip: '复制$label',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 36,
                    minHeight: 36,
                  ),
                ),
              ],
            ),
          ),
          // 字段内容
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: SelectableText(
              value,
              style: const TextStyle(
                fontSize: 14,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 根据字段名获取图标
  IconData _getFieldIcon(String fieldName) {
    switch (fieldName) {
      case 'goodsId':
        return Icons.tag;
      case 'sourceItemName':
        return Icons.shopping_cart;
      case 'sourceUrl':
        return Icons.link;
      case 'callbackData':
        return Icons.data_object;
      case 'sourceShopName':
        return Icons.store;
      case 'shopId':
        return Icons.store_mall_directory;
      default:
        return Icons.info_outline;
    }
  }

  /// 构建主页面任务卡片
  Widget _buildMainPageTaskCard(int index, dynamic task) {
    if (task is! Map<String, dynamic>) {
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.red.shade300),
        ),
        child: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red.shade600, size: 24),
            const SizedBox(width: 12),
            Text(
              '任务 $index: 数据格式错误',
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200, width: 1.5),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 任务头部
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade50, Colors.blue.shade100],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade600,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.shopping_bag_outlined,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '任务 $index',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                ),
              ],
            ),
          ),

          // 任务内容
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 主要字段：goodsId, sourceItemName, sourceUrl
                if (task['goodsId'] != null)
                  _buildSelectableField('商品ID', task['goodsId'].toString()),
                if (task['sourceItemName'] != null)
                  _buildSelectableField('商品名称', task['sourceItemName'].toString()),
                if (task['sourceUrl'] != null)
                  _buildSelectableField('商品链接', task['sourceUrl'].toString()),

                // 其他字段
                ...task.entries.where((entry) =>
                  !['goodsId', 'sourceItemName', 'sourceUrl'].contains(entry.key)
                ).map((entry) =>
                  _buildSelectableField(
                    _formatFieldName(entry.key),
                    entry.value?.toString() ?? 'null'
                  )
                ).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建可选择的字段（主页面用）
  Widget _buildSelectableField(String label, String value) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.copy,
                  size: 18,
                  color: Colors.grey.shade600,
                ),
                onPressed: () => _copyToClipboard(value, label),
                tooltip: '复制$label',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 36,
                  minHeight: 36,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          SelectableText(
            value,
            style: const TextStyle(
              fontSize: 14,
              height: 1.4,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }



  Future<void> _fetchTasks() async {
    if (_serverUrlController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入服务器地址')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final url = _serverUrlController.text.trim();

      // 构建请求体 - 注意参数名是goodsLink不是goodsLink
      final requestBody = {
        'token': _tokenController.text.trim(),
        'goodsLink': _goodsLink,
      };

      // 创建自定义HttpClient，跳过SSL证书验证
      final httpClient = HttpClient();
      httpClient.badCertificateCallback = (X509Certificate cert, String host, int port) => true;

      final request = await httpClient.postUrl(Uri.parse(url));
      request.headers.set('Content-Type', 'application/json');
      request.headers.set('User-Agent', 'ProxyPin/1.0');
      request.write(json.encode(requestBody));

      final httpResponse = await request.close();
      final responseBody = await httpResponse.transform(utf8.decoder).join();

      httpClient.close();

      if (httpResponse.statusCode == 200) {
        final data = json.decode(responseBody);
        final int code = data['code'] ?? -1;
        final String message = data['message'] ?? '';

        if (code == 0) {
          // 成功响应，检查是否有任务数据
          final List<TaskInfo> newTasks = [];
          List<dynamic> taskList = [];

          if (data['data'] != null) {
            final dataField = data['data'];
            if (dataField is Map) {
              // 单个任务对象，转换为数组
              taskList = [dataField];
            } else if (dataField is List) {
              // 多个任务数组
              taskList = dataField;
            }
          }

          // 解析任务数据
          for (var taskData in taskList) {
            try {
              newTasks.add(TaskInfo.fromJson(taskData));
            } catch (e) {
              print('解析任务数据失败: $e, 数据: $taskData');
            }
          }

          // 更新任务列表和过滤器配置
          await _updateTasksAndFilters(newTasks);

          setState(() {
            _tasks = newTasks;
            _rawTasks = taskList; // 保存原始任务数据
            _lastFetchTime = DateTime.now();
          });

          // 保存成功的配置
          await _saveData();

          // 只有当有任务时才不显示 SnackBar
          if (taskList.isNotEmpty) {
            // 有任务，不显示 SnackBar，任务会直接显示在页面上
            print('成功获取 ${taskList.length} 个任务');
          } else {
            // 没有任务数据，显示提示
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Row(
                  children: [
                    const Icon(Icons.info, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    const Text('暂无可用任务'),
                  ],
                ),
                backgroundColor: Colors.orange,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
        } else {
          // 服务器返回错误码
          String errorMessage = '状态码: $code';
          if (message.isNotEmpty) {
            errorMessage += ' - $message';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Expanded(child: Text('获取失败: $errorMessage')),
                ],
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } else {
        // 解析服务器响应获取错误信息
        String errorMessage = '';
        try {
          final data = json.decode(responseBody);
          if (data['message'] != null) {
            errorMessage = data['message'].toString();
          } else if (data['error'] != null) {
            errorMessage = data['error'].toString();
          }
        } catch (e) {
          // 如果解析失败，使用响应体作为错误信息
          errorMessage = responseBody.isNotEmpty ? responseBody : '未知错误';
        }

        // 组合状态码和错误信息
        String fullErrorMessage = '状态码: ${httpResponse.statusCode}';
        if (errorMessage.isNotEmpty) {
          fullErrorMessage += ' - $errorMessage';
        }

        // 显示简单的错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text('获取失败: $fullErrorMessage')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e, stackTrace) {
      // 显示简单的异常提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Expanded(child: Text('请求异常: ${e.toString()}')),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          duration: const Duration(seconds: 4),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updateTasksAndFilters(List<TaskInfo> tasks) async {
    // 将任务转换为捕获过滤器
    List<CaptureFilter> filters = [];

    for (var task in tasks) {
      if (task.isActive) {
        final filter = _taskToFilter(task);
        if (filter != null) {
          filters.add(filter);
        }
      }
    }

    // 更新拦截器配置
    CaptureFilterInterceptor.instance.updateFilters(filters);
  }

  CaptureFilter? _taskToFilter(TaskInfo task) {
    try {
      CaptureFilterType filterType;
      switch (task.type.toLowerCase()) {
        case 'url':
          filterType = CaptureFilterType.url;
          break;
        case 'domain':
          filterType = CaptureFilterType.domain;
          break;
        case 'response_body':
          filterType = CaptureFilterType.responseBody;
          break;
        case 'ip_upload':
          filterType = CaptureFilterType.ipUpload;
          break;
        default:
          return null;
      }

      return CaptureFilter(
        name: task.name,
        type: filterType,
        value: task.target,
        enabled: task.isActive,
        extractObject: task.extractObject,
        submitUrl: task.submitUrl,
        submitVariables: task.submitVariables,
      );
    } catch (e) {
      return null;
    }
  }

  void _showTaskDetails(TaskInfo task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(task.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('ID', task.id),
              _buildDetailRow('类型', task.type),
              _buildDetailRow('目标', task.target),
              _buildDetailRow('状态', task.isActive ? '激活' : '暂停'),
              if (task.description.isNotEmpty)
                _buildDetailRow('描述', task.description),
              if (task.extractObject != null)
                _buildDetailRow('提取对象', task.extractObject!),
              if (task.submitUrl != null)
                _buildDetailRow('提交URL', task.submitUrl!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

/// 任务信息
class TaskInfo {
  final String id;
  final String name;
  final String type;
  final String target;
  final bool isActive;
  final String description;
  final String? extractObject;
  final String? submitUrl;
  final List<SubmitVariable>? submitVariables;

  const TaskInfo({
    required this.id,
    required this.name,
    required this.type,
    required this.target,
    required this.isActive,
    this.description = '',
    this.extractObject,
    this.submitUrl,
    this.submitVariables,
  });

  factory TaskInfo.fromJson(Map<String, dynamic> json) {
    return TaskInfo(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      type: json['type']?.toString() ?? '',
      target: json['target']?.toString() ?? '',
      isActive: json['isActive'] == true || json['active'] == true,
      description: json['description']?.toString() ?? '',
      extractObject: json['extractObject']?.toString(),
      submitUrl: json['submitUrl']?.toString(),
      submitVariables: json['submitVariables'] != null
          ? (json['submitVariables'] as List).map((v) => SubmitVariable.fromJson(v)).toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'target': target,
      'isActive': isActive,
      'description': description,
      'extractObject': extractObject,
      'submitUrl': submitUrl,
      'submitVariables': submitVariables?.map((v) => v.toJson()).toList(),
    };
  }
}
