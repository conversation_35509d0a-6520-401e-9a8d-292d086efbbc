 /*
 * Copyright 2023 <PERSON><PERSON> Wang All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:proxypin/network/http/http.dart';
import 'package:http/http.dart' as http;
import 'package:proxypin/network/components/capture_filter.dart';

/// 捕获过滤器配置页面
/// <AUTHOR>
class CaptureFilterConfig extends StatefulWidget {
  final List<CaptureFilter> currentFilters;
  final Function(List<CaptureFilter>) onFiltersChanged;

  const CaptureFilterConfig({
    super.key,
    required this.currentFilters,
    required this.onFiltersChanged,
  });

  @override
  State<CaptureFilterConfig> createState() => _CaptureFilterConfigState();
}

class _CaptureFilterConfigState extends State<CaptureFilterConfig> {
  List<CaptureFilter> _filters = [];

  // 功能开关配置
  static const bool _enableEditAndDelete = false; // 设置为 true 启用编辑和删除按钮

  // 默认过滤器配置 - 可以手动编辑这里来修改默认配置
  // 配置说明：
  // - name: 过滤器名称
  // - type: 过滤器类型 (ipUpload = 指定IP地址上传)
  // - value: 匹配的URL (支持包含匹配)
  // - extractObject: 要提取的对象字段名
  // - submitUrl: 上传目标URL
  // - submitVariables: 上传时的固定参数
  static final List<CaptureFilter> _defaultFilters = [
    CaptureFilter(
      name: 'test',
      type: CaptureFilterType.ipUpload,
      value: 'https://mobile.yangkeduo.cc',
      enabled: true,
      extractObject: 'rawData',
      submitUrl: 'http://***********:1001',
      submitVariables: [
        SubmitVariable(name: 'token', defaultValue: '222222'),
        SubmitVariable(name: 'type', defaultValue: 'Mini_programs'),
      ],
    ),
    // 可以在这里添加更多默认过滤器
    // CaptureFilter(
    //   name: '另一个过滤器',
    //   type: CaptureFilterType.ipUpload,
    //   value: 'https://api.example.com',
    //   enabled: true,
    //   extractObject: 'data',
    //   submitUrl: 'http://your-server.com/api',
    //   submitVariables: [
    //     SubmitVariable(name: 'apiKey', defaultValue: 'your-api-key'),
    //   ],
    // ),
  ];

  @override
  void initState() {
    super.initState();
    _filters = List.from(widget.currentFilters);

    // 如果没有过滤器，添加默认过滤器
    if (_filters.isEmpty) {
      _filters.addAll(_defaultFilters.map((filter) => filter.copyWith()));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('捕获过滤器配置'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddFilterDialog,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: () {
              // 更新拦截器配置
              CaptureFilterInterceptor.instance.updateFilters(_filters);
              // 回调通知外部
              widget.onFiltersChanged(_filters);
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 过滤器列表
          Expanded(
            child: _filters.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.filter_alt_off,
                          size: 64,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          '暂无过滤器',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '点击上方按钮添加第一个过滤器',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: _filters.length,
                    itemBuilder: (context, index) {
                      final filter = _filters[index];
                      return _buildFilterItem(filter, index);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterItem(CaptureFilter filter, int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部：名称和开关
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: filter.enabled ? Colors.green : Colors.grey,
                  radius: 16,
                  child: Icon(
                    _getFilterIcon(filter.type),
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    filter.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Switch(
                  value: filter.enabled,
                  onChanged: (value) {
                    setState(() {
                      _filters[index] = filter.copyWith(enabled: value);
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // 过滤条件描述
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                filter.description,
                style: const TextStyle(fontSize: 14),
              ),
            ),
            
            // 操作按钮
            const SizedBox(height: 12),
            // 编辑和删除按钮 - 通过 _enableEditAndDelete 控制显示
            if (_enableEditAndDelete) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _editFilter(index),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('编辑'),
                  ),
                  const SizedBox(width: 8),
                  TextButton.icon(
                    onPressed: () => _deleteFilter(index),
                    icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                    label: const Text('删除', style: TextStyle(color: Colors.red)),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _editFilter(int index) {
    showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        existingFilter: _filters[index],
        onFilterCreated: (filter) {
          setState(() {
            _filters[index] = filter;
          });
        },
      ),
    );
  }

  void _deleteFilter(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除过滤器 "${_filters[index].name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _filters.removeAt(index);
              });
              Navigator.pop(context);
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  IconData _getFilterIcon(CaptureFilterType type) {
    switch (type) {
      case CaptureFilterType.url:
        return Icons.link;
      case CaptureFilterType.method:
        return Icons.http;
      case CaptureFilterType.status:
        return Icons.code;
      case CaptureFilterType.contentType:
        return Icons.description;
      case CaptureFilterType.domain:
        return Icons.domain;
      case CaptureFilterType.responseBody:
        return Icons.data_object;
      case CaptureFilterType.ipUpload:
        return Icons.upload;
    }
  }

  String _getFilterTypeName(CaptureFilterType type) {
    switch (type) {
      case CaptureFilterType.url:
        return 'URL包含';
      case CaptureFilterType.method:
        return 'HTTP方法';
      case CaptureFilterType.status:
        return '状态码';
      case CaptureFilterType.contentType:
        return '内容类型';
      case CaptureFilterType.domain:
        return '域名';
      case CaptureFilterType.responseBody:
        return '响应内容包含';
      case CaptureFilterType.ipUpload:
        return '指定IP地址上传';
    }
  }

  void _showAddFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        onFilterCreated: (filter) {
          setState(() {
            _filters.add(filter);
          });
        },
      ),
    );
  }
}

/// 过滤器添加/编辑对话框
class _FilterDialog extends StatefulWidget {
  final CaptureFilter? existingFilter;
  final Function(CaptureFilter) onFilterCreated;

  const _FilterDialog({
    this.existingFilter,
    required this.onFilterCreated,
  });

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  final _nameController = TextEditingController();
  final _valueController = TextEditingController();
  final _secondaryValueController = TextEditingController();
  final _extractObjectController = TextEditingController();
  final _submitUrlController = TextEditingController();

  CaptureFilterType _selectedType = CaptureFilterType.url;
  CaptureFilterType? _selectedSecondaryType;
  bool _enabled = true;
  bool _useSecondaryFilter = false;
  List<SubmitVariable> _submitVariables = [];

  @override
  void initState() {
    super.initState();
    if (widget.existingFilter != null) {
      final filter = widget.existingFilter!;
      _nameController.text = filter.name;
      _valueController.text = filter.value;
      _selectedType = filter.type;
      _enabled = filter.enabled;

      // 组合过滤器
      if (filter.secondaryType != null && filter.secondaryValue != null) {
        _useSecondaryFilter = true;
        _selectedSecondaryType = filter.secondaryType;
        _secondaryValueController.text = filter.secondaryValue!;
      }

      // IP上传配置
      if (filter.type == CaptureFilterType.ipUpload) {
        if (filter.extractObject != null) _extractObjectController.text = filter.extractObject!;
        if (filter.submitUrl != null) _submitUrlController.text = filter.submitUrl!;
        if (filter.submitVariables != null) _submitVariables = List.from(filter.submitVariables!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          Icon(
            widget.existingFilter == null ? Icons.add_circle : Icons.edit,
            color: Colors.blue,
          ),
          const SizedBox(width: 8),
          Text(
            widget.existingFilter == null ? '添加过滤器' : '编辑过滤器',
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '过滤器名称',
                hintText: '例如：API请求过滤',
                prefixIcon: Icon(Icons.label),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 过滤器类型选择
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: DropdownButtonFormField<CaptureFilterType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: '过滤器类型',
                prefixIcon: Icon(Icons.filter_alt),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
              items: CaptureFilterType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Row(
                    children: [
                      Icon(_getFilterTypeIcon(type), size: 20, color: Colors.grey),
                      const SizedBox(width: 8),
                      Text(_getFilterTypeName(type)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                  _valueController.clear();
                });
              },
            ),
          ),
          const SizedBox(height: 16),

          // 过滤器值
          Container(
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: TextField(
              controller: _valueController,
              decoration: InputDecoration(
                labelText: '过滤器值',
                hintText: _getFilterHint(_selectedType),
                prefixIcon: Icon(_getFilterTypeIcon(_selectedType)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),





          // IP上传配置
          if (_selectedType == CaptureFilterType.ipUpload) ...[
            const SizedBox(height: 24),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.upload, color: Colors.blue),
                      const SizedBox(width: 8),
                      const Text(
                        'IP上传配置',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withOpacity(0.3)),
                    ),
                    child: TextField(
                      controller: _extractObjectController,
                      decoration: const InputDecoration(
                        labelText: '要截取的对象值',
                        hintText: '例如：rawData',
                        prefixIcon: Icon(Icons.code),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.withOpacity(0.3)),
                    ),
                    child: TextField(
                      controller: _submitUrlController,
                      decoration: const InputDecoration(
                        labelText: '提交URL',
                        hintText: '例如：http://*************:8080/upload',
                        prefixIcon: Icon(Icons.link),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 上传变量配置
                  Row(
                    children: [
                      const Text(
                        '上传变量',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: _addSubmitVariable,
                        icon: const Icon(Icons.add, size: 16),
                        label: const Text('添加变量'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // 变量列表
                  if (_submitVariables.isNotEmpty) ...[
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.withOpacity(0.3)),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        children: _submitVariables.asMap().entries.map((entry) {
                          final index = entry.key;
                          final variable = entry.value;
                          return ListTile(
                            dense: true,
                            title: Text(variable.name),
                            subtitle: Text('默认值: ${variable.defaultValue}'),
                            trailing: IconButton(
                              icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                              onPressed: () => _removeSubmitVariable(index),
                            ),
                            onTap: () => _editSubmitVariable(index),
                          );
                        }).toList(),
                      ),
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.withOpacity(0.3)),
                      ),
                      child: const Text(
                        '暂无上传变量\n点击"添加变量"按钮添加',
                        style: TextStyle(color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: _createFilter,
          child: Text(widget.existingFilter == null ? '添加' : '保存'),
        ),
      ],
    );
  }

  String _getFilterTypeName(CaptureFilterType type) {
    switch (type) {
      case CaptureFilterType.url:
        return 'URL包含';
      case CaptureFilterType.method:
        return 'HTTP方法';
      case CaptureFilterType.status:
        return '状态码';
      case CaptureFilterType.contentType:
        return '内容类型';
      case CaptureFilterType.domain:
        return '域名';
      case CaptureFilterType.responseBody:
        return '响应内容包含';
      case CaptureFilterType.ipUpload:
        return '指定IP地址上传';
    }
  }

  IconData _getFilterTypeIcon(CaptureFilterType type) {
    switch (type) {
      case CaptureFilterType.url:
        return Icons.link;
      case CaptureFilterType.method:
        return Icons.http;
      case CaptureFilterType.status:
        return Icons.code;
      case CaptureFilterType.contentType:
        return Icons.description;
      case CaptureFilterType.domain:
        return Icons.domain;
      case CaptureFilterType.responseBody:
        return Icons.data_object;
      case CaptureFilterType.ipUpload:
        return Icons.upload;
    }
  }

  String _getFilterHint(CaptureFilterType type) {
    switch (type) {
      case CaptureFilterType.url:
        return '例如：/api/';
      case CaptureFilterType.method:
        return '例如：GET,POST';
      case CaptureFilterType.status:
        return '例如：200,404';
      case CaptureFilterType.contentType:
        return '例如：application/json';
      case CaptureFilterType.domain:
        return '例如：api.example.com';
      case CaptureFilterType.responseBody:
        return '例如：rawData, window.';
      case CaptureFilterType.ipUpload:
        return '例如：http://*************:8080/upload';
    }
  }

  void _createFilter() {
    if (_nameController.text.trim().isEmpty || _valueController.text.trim().isEmpty) {
      return;
    }

    // 如果启用了第二个过滤器，检查其值是否有效
    if (_useSecondaryFilter) {
      if (_selectedSecondaryType == null || _secondaryValueController.text.trim().isEmpty) {
        return;
      }
    }

    final filter = CaptureFilter(
      name: _nameController.text.trim(),
      type: _selectedType,
      value: _valueController.text.trim(),
      enabled: _enabled,
      secondaryType: _useSecondaryFilter ? _selectedSecondaryType : null,
      secondaryValue: _useSecondaryFilter ? _secondaryValueController.text.trim() : null,
      // IP上传配置
      extractObject: _selectedType == CaptureFilterType.ipUpload ? _extractObjectController.text.trim() : null,
      submitUrl: _selectedType == CaptureFilterType.ipUpload ? _submitUrlController.text.trim() : null,
      submitVariables: _selectedType == CaptureFilterType.ipUpload ? List.from(_submitVariables) : null,
    );

    widget.onFilterCreated(filter);
    Navigator.pop(context);
  }

  void _addSubmitVariable() {
    showDialog(
      context: context,
      builder: (context) => _SubmitVariableDialog(
        onVariableCreated: (variable) {
          setState(() {
            _submitVariables.add(variable);
          });
        },
      ),
    );
  }

  void _editSubmitVariable(int index) {
    showDialog(
      context: context,
      builder: (context) => _SubmitVariableDialog(
        existingVariable: _submitVariables[index],
        onVariableCreated: (variable) {
          setState(() {
            _submitVariables[index] = variable;
          });
        },
      ),
    );
  }

  void _removeSubmitVariable(int index) {
    setState(() {
      _submitVariables.removeAt(index);
    });
  }
}

/// 上传变量配置对话框
class _SubmitVariableDialog extends StatefulWidget {
  final SubmitVariable? existingVariable;
  final Function(SubmitVariable) onVariableCreated;

  const _SubmitVariableDialog({
    this.existingVariable,
    required this.onVariableCreated,
  });

  @override
  State<_SubmitVariableDialog> createState() => _SubmitVariableDialogState();
}

class _SubmitVariableDialogState extends State<_SubmitVariableDialog> {
  final _nameController = TextEditingController();
  final _defaultValueController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.existingVariable != null) {
      _nameController.text = widget.existingVariable!.name;
      _defaultValueController.text = widget.existingVariable!.defaultValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.existingVariable == null ? '添加上传变量' : '编辑上传变量'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: '变量名',
              hintText: '例如：userId, deviceId',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _defaultValueController,
            decoration: const InputDecoration(
              labelText: '默认值',
              hintText: '例如：12345, unknown',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: _createVariable,
          child: Text(widget.existingVariable == null ? '添加' : '保存'),
        ),
      ],
    );
  }

  void _createVariable() {
    if (_nameController.text.trim().isEmpty) {
      return;
    }

    final variable = SubmitVariable(
      name: _nameController.text.trim(),
      defaultValue: _defaultValueController.text.trim(),
    );

    widget.onVariableCreated(variable);
    Navigator.pop(context);
  }
}

/// 捕获过滤器类型
enum CaptureFilterType {
  url,
  ipUpload,
  method,
  status,
  contentType,
  domain,
  responseBody,
}

/// 捕获过滤器
class CaptureFilter {
  final String name;
  final CaptureFilterType type;
  final String value;
  final bool enabled;

  // 组合过滤器字段
  final CaptureFilterType? secondaryType;
  final String? secondaryValue;

  // IP上传配置字段
  final String? extractObject;
  final String? submitUrl;
  final List<SubmitVariable>? submitVariables;

  const CaptureFilter({
    required this.name,
    required this.type,
    required this.value,
    this.enabled = true,
    this.secondaryType,
    this.secondaryValue,
    this.extractObject,
    this.submitUrl,
    this.submitVariables,
  });

  String get description {
    String primary = '';
    switch (type) {
      case CaptureFilterType.url:
        primary = 'URL包含: $value';
        break;
      case CaptureFilterType.method:
        primary = 'HTTP方法: $value';
        break;
      case CaptureFilterType.status:
        primary = '状态码: $value';
        break;
      case CaptureFilterType.contentType:
        primary = '内容类型: $value';
        break;
      case CaptureFilterType.domain:
        primary = '域名: $value';
        break;
      case CaptureFilterType.responseBody:
        primary = '响应内容包含: $value';
        break;
      case CaptureFilterType.ipUpload:
        primary = '上传到: $value';
        break;
    }

    if (secondaryType != null && secondaryValue != null) {
      String secondary = '';
      switch (secondaryType!) {
        case CaptureFilterType.url:
          secondary = 'URL包含: $secondaryValue';
          break;
        case CaptureFilterType.method:
          secondary = 'HTTP方法: $secondaryValue';
          break;
        case CaptureFilterType.status:
          secondary = '状态码: $secondaryValue';
          break;
        case CaptureFilterType.contentType:
          secondary = '内容类型: $secondaryValue';
          break;
        case CaptureFilterType.domain:
          secondary = '域名: $secondaryValue';
          break;
        case CaptureFilterType.responseBody:
          secondary = '响应内容包含: $secondaryValue';
          break;
        case CaptureFilterType.ipUpload:
          secondary = '上传到: $secondaryValue';
          break;
      }
      return '$primary 且 $secondary';
    }

    return primary;
  }

  bool matches(HttpRequest request) {
    if (!enabled) return true;

    // 检查主要过滤条件
    bool primaryMatch = _matchesType(request, type, value);

    // 如果没有次要过滤条件，只需要主要条件匹配
    if (secondaryType == null || secondaryValue == null) {
      return primaryMatch;
    }

    // 如果有次要过滤条件，需要两个条件都匹配
    bool secondaryMatch = _matchesType(request, secondaryType!, secondaryValue!);
    return primaryMatch && secondaryMatch;
  }

  bool _matchesType(HttpRequest request, CaptureFilterType filterType, String filterValue) {
    switch (filterType) {
      case CaptureFilterType.url:
        final requestUrl = request.requestUrl.toLowerCase();
        final value = filterValue.toLowerCase();

        // 如果过滤值包含协议，直接匹配
        if (value.startsWith('http://') || value.startsWith('https://')) {
          return requestUrl.contains(value);
        }

        // 如果过滤值不包含协议，忽略协议进行匹配
        final urlWithoutProtocol = requestUrl.replaceFirst(RegExp(r'^https?://'), '');
        return urlWithoutProtocol.contains(value);

      case CaptureFilterType.method:
        final methods = filterValue.split(',').map((m) => m.trim().toUpperCase()).toList();
        return methods.contains(request.method.name.toUpperCase());
      case CaptureFilterType.status:
        if (request.response == null) return false;
        final codes = filterValue.split(',').map((c) => int.tryParse(c.trim())).where((c) => c != null).toList();
        return codes.contains(request.response!.status.code);
      case CaptureFilterType.contentType:
        if (request.response == null) return false;
        final contentType = request.response!.headers.contentType?.toLowerCase() ?? '';
        return contentType.contains(filterValue.toLowerCase());
      case CaptureFilterType.domain:
        final domain = request.remoteDomain() ?? '';
        return domain.toLowerCase().contains(filterValue.toLowerCase());
      case CaptureFilterType.responseBody:
        if (request.response == null) return false;
        final responseBody = (request.response!.bodyAsString ?? '').toLowerCase();
        return responseBody.contains(filterValue.toLowerCase());
      case CaptureFilterType.ipUpload:
        // IP上传类型根据过滤值进行过滤（可以是IP地址、域名或URL）
        final requestUrl = request.requestUrl.toLowerCase();
        final value = filterValue.toLowerCase();

        // 检查是否是IP地址格式
        final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$');
        if (ipRegex.hasMatch(filterValue)) {
          // 如果是IP地址，检查请求的主机是否匹配
          final host = request.hostAndPort?.host ?? '';
          return host == filterValue;
        }

        // 如果不是IP地址，按URL包含逻辑处理
        if (value.startsWith('http://') || value.startsWith('https://')) {
          return requestUrl.contains(value);
        }

        // 如果过滤值不包含协议，忽略协议进行匹配
        final urlWithoutProtocol = requestUrl.replaceFirst(RegExp(r'^https?://'), '');
        return urlWithoutProtocol.contains(value);
    }
  }

  /// 检查是否需要上传
  bool get shouldUpload {
    return type == CaptureFilterType.ipUpload &&
           submitUrl != null &&
           submitUrl!.isNotEmpty;
  }

  /// 执行上传操作
  Future<UploadResult> uploadData(HttpRequest request) async {
    if (!shouldUpload) {
      return UploadResult(success: false, message: '未配置上传');
    }

    try {
      // 首先提取对象值
      if (extractObject == null || extractObject!.isEmpty) {
        return UploadResult(
          success: false,
          message: '未配置要截取的对象值',
          uploadUrl: submitUrl,
        );
      }

      final responseBody = request.response?.bodyAsString;
      if (responseBody == null || responseBody.isEmpty) {
        return UploadResult(
          success: false,
          message: '响应内容为空，无法提取数据',
          uploadUrl: submitUrl,
        );
      }

      // 提取对象值
      final extractedValues = _extractObjectValues(responseBody, extractObject!);
      if (extractedValues.isEmpty) {
        return UploadResult(
          success: false,
          message: '未能提取到指定的对象值',
          uploadUrl: submitUrl,
        );
      }

      // 构建上传数据
      Map<String, dynamic> uploadData = {};

      // 添加提交变量（作为固定参数）
      if (submitVariables != null) {
        for (var variable in submitVariables!) {
          uploadData[variable.name] = variable.defaultValue;
        }
      }

      // 如果只提取了rawData，直接使用rawData作为data字段
      if (extractedValues.containsKey('rawData') && extractedValues.length == 1) {
        // 尝试解析rawData为JSON对象
        try {
          final rawDataString = extractedValues['rawData'] as String;
          final rawDataJson = json.decode(rawDataString);
          uploadData['data'] = rawDataJson;  // 直接使用解析后的JSON对象
        } catch (e) {
          // 如果解析失败，使用原始字符串
          uploadData['data'] = extractedValues['rawData'];
        }
      } else {
        // 如果提取了多个字段，使用原来的方式
        uploadData['data'] = extractedValues;
      }

      // 发送HTTP请求到后端
      final response = await _sendUploadRequest(uploadData);

      return UploadResult(
        success: response['success'] == true,
        message: json.encode(response), // 直接显示返回的JSON
        data: extractedValues, // 只显示截取的数据
        serverResponse: response, // 保存完整的服务器响应
        uploadUrl: submitUrl, // 上传URL
        uploadData: uploadData, // 上传的完整对象
      );

    } catch (e, stackTrace) {
      return UploadResult(
        success: false,
        message: '上传失败: $e',
        uploadUrl: submitUrl,
      );
    }
  }

  /// 提取对象值
  Map<String, dynamic> _extractObjectValues(String responseBody, String extractFields) {
    Map<String, dynamic> result = {};

    try {
      // 首先尝试作为JSON解析
      final jsonData = json.decode(responseBody);
      final fields = extractFields.split(',').map((f) => f.trim()).toList();

      for (String field in fields) {
        if (jsonData.containsKey(field)) {
          result[field] = jsonData[field];
        }
      }
    } catch (e) {
      // 如果不是JSON，尝试正则表达式提取
      final fields = extractFields.split(',').map((f) => f.trim()).toList();
      for (String field in fields) {
        String? extractedValue = _extractFieldWithRegex(responseBody, field);
        if (extractedValue != null) {
          result[field] = extractedValue;
        }
      }
    }

    return result;
  }

  /// 使用正则表达式提取字段值
  String? _extractFieldWithRegex(String text, String fieldName) {
    // 特殊处理rawData，因为它通常是JavaScript赋值语句
    if (fieldName.toLowerCase() == 'rawdata') {
      return _extractRawData(text);
    }

    // 对于其他字段，使用简单的正则表达式
    List<RegExp> patterns = [
      RegExp('"$fieldName"\\s*:\\s*"([^"]*)"'),  // JSON字符串
      RegExp('"$fieldName"\\s*:\\s*([^,}\\]]+)'), // JSON值
      RegExp('$fieldName\\s*=\\s*"([^"]*)"'),     // JS字符串
      RegExp('$fieldName\\s*=\\s*([^;,\\s]+)'),   // JS值
    ];

    for (RegExp pattern in patterns) {
      final match = pattern.firstMatch(text);
      if (match != null && match.group(1) != null) {
        String value = match.group(1)!.trim();
        return value;
      }
    }

    return null;
  }

  /// 专门提取rawData的方法
  String? _extractRawData(String text) {
    // 查找window.rawData=的位置
    int startIndex = text.indexOf('window.rawData=');
    if (startIndex == -1) {
      startIndex = text.indexOf('rawData=');
    }
    if (startIndex == -1) {
      return null;
    }

    // 找到等号后面的位置
    int equalPos = text.indexOf('=', startIndex);
    if (equalPos == -1) return null;

    // 跳过等号和空白字符，找到{的位置
    int braceStart = -1;
    for (int i = equalPos + 1; i < text.length; i++) {
      if (text[i] == '{') {
        braceStart = i;
        break;
      }
    }

    if (braceStart == -1) {
      return null;
    }

    // 简单粗暴的方法：找到对应的结束}
    int braceCount = 0;
    int braceEnd = -1;

    for (int i = braceStart; i < text.length; i++) {
      String char = text[i];
      if (char == '{') {
        braceCount++;
      } else if (char == '}') {
        braceCount--;
        if (braceCount == 0) {
          braceEnd = i;
          break;
        }
      }
    }

    if (braceEnd == -1) {
      return null;
    }

    String result = text.substring(braceStart, braceEnd + 1);
    return result;
  }

  /// 发送上传请求
  Future<Map<String, dynamic>> _sendUploadRequest(Map<String, dynamic> data) async {
    try {
      developer.log('发送上传请求到: $submitUrl', name: 'CaptureFilter.Upload');
      developer.log('上传数据:\n${const JsonEncoder.withIndent('  ').convert(data)}', name: 'CaptureFilter.UploadRequest');

      final response = await http.post(
        Uri.parse(submitUrl!),
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'ProxyPin/1.0',
        },
        body: json.encode(data),
      ).timeout(const Duration(seconds: 30));

      developer.log('上传响应状态码: ${response.statusCode}', name: 'CaptureFilter.UploadResponse');
      developer.log('上传响应内容:\n${response.body}', name: 'CaptureFilter.UploadResponse');

      if (response.statusCode == 200) {
        try {
          return json.decode(response.body);
        } catch (e) {
          return {
            'success': true,
            'message': response.body,
            'statusCode': response.statusCode,
          };
        }
      } else {
        // 尝试解析服务器返回的JSON，如果失败则使用包装格式
        try {
          final serverResponse = json.decode(response.body);
          // 如果服务器返回的是有效JSON，直接使用
          return serverResponse;
        } catch (e) {
          // 如果不是有效JSON，使用包装格式
          return {
            'success': false,
            'message': 'HTTP ${response.statusCode}: ${response.body}',
            'statusCode': response.statusCode,
          };
        }
      }
    } catch (e, stackTrace) {
      developer.log('发送请求异常: $e', name: 'CaptureFilter.UploadError', error: e, stackTrace: stackTrace);
      return {
        'success': false,
        'message': '网络错误: $e',
      };
    }
  }

  CaptureFilter copyWith({
    String? name,
    CaptureFilterType? type,
    String? value,
    bool? enabled,
    CaptureFilterType? secondaryType,
    String? secondaryValue,
    String? extractObject,
    String? submitUrl,
    List<SubmitVariable>? submitVariables,
  }) {
    return CaptureFilter(
      name: name ?? this.name,
      type: type ?? this.type,
      value: value ?? this.value,
      enabled: enabled ?? this.enabled,
      secondaryType: secondaryType ?? this.secondaryType,
      secondaryValue: secondaryValue ?? this.secondaryValue,
      extractObject: extractObject ?? this.extractObject,
      submitUrl: submitUrl ?? this.submitUrl,
      submitVariables: submitVariables ?? this.submitVariables,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'type': type.name,
      'value': value,
      'enabled': enabled,
      'secondaryType': secondaryType?.name,
      'secondaryValue': secondaryValue,
      'extractObject': extractObject,
      'submitUrl': submitUrl,
      'submitVariables': submitVariables?.map((v) => v.toJson()).toList(),
    };
  }

  factory CaptureFilter.fromJson(Map<String, dynamic> json) {
    return CaptureFilter(
      name: json['name'] ?? '',
      type: CaptureFilterType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => CaptureFilterType.url,
      ),
      value: json['value'] ?? '',
      enabled: json['enabled'] ?? true,
      secondaryType: json['secondaryType'] != null
          ? CaptureFilterType.values.firstWhere(
              (e) => e.name == json['secondaryType'],
              orElse: () => CaptureFilterType.url,
            )
          : null,
      secondaryValue: json['secondaryValue'],
      extractObject: json['extractObject'],
      submitUrl: json['submitUrl'],
      submitVariables: json['submitVariables'] != null
          ? (json['submitVariables'] as List)
              .map((v) => SubmitVariable.fromJson(v))
              .toList()
          : null,
    );
  }
}

/// 提交变量
class SubmitVariable {
  final String name;
  final String defaultValue;

  const SubmitVariable({
    required this.name,
    required this.defaultValue,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'defaultValue': defaultValue,
    };
  }

  factory SubmitVariable.fromJson(Map<String, dynamic> json) {
    return SubmitVariable(
      name: json['name'] ?? '',
      defaultValue: json['defaultValue'] ?? '',
    );
  }
}

/// 上传结果
class UploadResult {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;
  final Map<String, dynamic>? serverResponse;
  final String? uploadUrl;
  final Map<String, dynamic>? uploadData;

  const UploadResult({
    required this.success,
    required this.message,
    this.data,
    this.serverResponse,
    this.uploadUrl,
    this.uploadData,
  });
}

/// IP上传响应结果
class UploadResponseResult {
  final String filterName;
  final String requestUrl;
  final String uploadUrl;
  final Map<String, dynamic> extractedData;
  final Map<String, dynamic> serverResponse;
  final bool success;
  final String message;
  final DateTime timestamp;

  const UploadResponseResult({
    required this.filterName,
    required this.requestUrl,
    required this.uploadUrl,
    required this.extractedData,
    required this.serverResponse,
    required this.success,
    required this.message,
    required this.timestamp,
  });
}
