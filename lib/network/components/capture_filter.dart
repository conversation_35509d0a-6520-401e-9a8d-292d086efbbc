import 'dart:convert';
import 'dart:developer' as developer;
import 'package:proxypin/network/components/interceptor.dart';
import 'package:proxypin/network/http/http.dart';
import 'package:proxypin/network/util/logger.dart';
import 'package:proxypin/ui/mobile/request/capture_filter_config.dart';

/// 自定义捕获过滤器拦截器
/// 用于处理自定义的捕获过滤器逻辑
class CaptureFilterInterceptor extends Interceptor {
  static CaptureFilterInterceptor? _instance;
  static CaptureFilterInterceptor get instance => _instance ??= CaptureFilterInterceptor._();
  
  CaptureFilterInterceptor._();

  @override
  int get priority => 500; // 在其他拦截器之后执行

  // 存储过滤器配置
  List<CaptureFilter> _filters = [];

  // 请求捕获回调
  Function(HttpRequest)? onRequestCaptured;

  // IP上传响应回调
  Function(UploadResponseResult)? onUploadResponse;

  /// 更新过滤器配置
  void updateFilters(List<CaptureFilter> filters) {
    _filters = List.from(filters);
    logger.d('更新捕获过滤器配置: ${_filters.length}个过滤器');
  }

  @override
  Future<HttpResponse?> onResponse(HttpRequest request, HttpResponse response) async {
    // 检查是否有启用的过滤器
    final enabledFilters = _filters.where((f) => f.enabled).toList();
    if (enabledFilters.isEmpty) {
      return response;
    }

    // 检查每个过滤器
    for (final filter in enabledFilters) {
      try {
        // 检查过滤器是否匹配
        bool matches = filter.matches(request);

        if (matches) {
          // 只显示匹配的过滤器信息
          logger.i('🎯 过滤器匹配: ${filter.name} -> ${request.requestUrl}');

          print('🎯 过滤器匹配详情:');
          print('   - 过滤器名称: ${filter.name}');
          print('   - 过滤器类型: ${filter.type}');
          print('   - 是否IP上传: ${filter.type == CaptureFilterType.ipUpload}');
          print('   - 应该上传: ${filter.shouldUpload}');
          print('   - 上传URL: ${filter.submitUrl}');
          print('   - 有上传回调: ${onUploadResponse != null}');
          print('   - 请求URL: ${request.requestUrl}');

          // 通知自定义捕获页面
          if (onRequestCaptured != null) {
            onRequestCaptured!(request);
          }

          // 如果匹配且需要上传，异步执行上传操作（不阻塞请求处理）
          if (filter.shouldUpload) {
            // 异步执行上传，不等待结果，避免阻塞请求处理
            _performAsyncUpload(filter, request);
          }
        }
      } catch (e, stackTrace) {
        logger.e('过滤器检查异常: ${filter.name}', error: e, stackTrace: stackTrace);
      }
    }

    return response;
  }

  /// 异步执行上传操作，不阻塞主请求流程
  void _performAsyncUpload(CaptureFilter filter, HttpRequest request) {
    // 使用异步操作，不等待结果，让原始请求立即通过
    Future.microtask(() async {
      try {
        developer.log('🚀 开始异步上传操作', name: 'CaptureFilter');

        final result = await filter.uploadData(request);

        developer.log('✅ 上传操作完成', name: 'CaptureFilter');

        // 显示上传结果（无论成功还是失败）
        if (result.success) {
          logger.i('✅ 数据上传成功: ${filter.name}');
        } else {
          logger.w('❌ 数据上传失败: ${filter.name} - ${result.message}');
        }

        // 如果是IP上传类型，通知UI显示响应结果（无论成功还是失败）
        if (filter.type == CaptureFilterType.ipUpload && onUploadResponse != null) {
          final uploadResult = UploadResponseResult(
            filterName: filter.name,
            requestUrl: request.requestUrl,
            uploadUrl: result.uploadUrl ?? '',
            extractedData: result.data ?? {},
            serverResponse: result.serverResponse ?? {},
            success: result.success,
            message: result.message,
            timestamp: DateTime.now(),
          );

          onUploadResponse!(uploadResult);
        }
      } catch (e, stackTrace) {
        developer.log('上传操作异常', name: 'CaptureFilter', error: e, stackTrace: stackTrace);
        logger.e('💥 上传操作异常: ${filter.name}', error: e, stackTrace: stackTrace);
      }
    });
  }


}
