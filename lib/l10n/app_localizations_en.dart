// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get requests => 'Requests';

  @override
  String get favorites => 'Favorites';

  @override
  String get history => 'History';

  @override
  String get toolbox => 'Toolbox';

  @override
  String get me => 'Me';

  @override
  String get preference => 'Preferences';

  @override
  String get feedback => 'Feedback';

  @override
  String get about => 'About';

  @override
  String get filter => 'Proxy Filter';

  @override
  String get script => 'Script';

  @override
  String get share => 'Share';

  @override
  String get port => 'Port: ';

  @override
  String get proxy => 'Proxy';

  @override
  String get externalProxy => 'External Proxy';

  @override
  String get username => 'Username';

  @override
  String get password => 'Password';

  @override
  String get proxySetting => 'Proxy Setting';

  @override
  String get systemProxy => 'Set as System Proxy';

  @override
  String get enabledHTTP2 => 'Enable HTTP2';

  @override
  String get serverNotStart => 'Proxy server not started';

  @override
  String get download => 'Download';

  @override
  String get start => 'Start';

  @override
  String get stop => 'Stop';

  @override
  String get clear => 'Clear';

  @override
  String get httpsProxy => 'HTTPS Proxy';

  @override
  String get setting => 'Settings';

  @override
  String get mobileConnect => 'Mobile Connect';

  @override
  String get connectRemote => 'Connect Remote';

  @override
  String get remoteDevice => 'Remote Device';

  @override
  String get remoteDeviceList => 'Remote Device List';

  @override
  String get myQRCode => 'My QR Code';

  @override
  String get theme => 'Theme';

  @override
  String get followSystem => 'Follow System';

  @override
  String get themeColor => 'Theme Color';

  @override
  String get themeLight => 'Light';

  @override
  String get themeDark => 'Dark';

  @override
  String get language => 'Language';

  @override
  String get autoStartup => 'Auto Start Recording Traffic';

  @override
  String get autoStartupDescribe => 'Automatically start recording traffic when the program starts';

  @override
  String get copied => 'Copied to clipboard';

  @override
  String get cancel => 'Cancel';

  @override
  String get close => 'Close';

  @override
  String get save => 'Save';

  @override
  String get confirm => 'Confirm';

  @override
  String get confirmTitle => 'Confirm operation';

  @override
  String get confirmContent => 'Are you sure about this operation?';

  @override
  String get addSuccess => 'Successfully added';

  @override
  String get saveSuccess => 'Saved successfully';

  @override
  String get operationSuccess => 'Operation succeeded';

  @override
  String get import => 'Import';

  @override
  String get importSuccess => 'Import successful';

  @override
  String get importFailed => 'Import failed';

  @override
  String get export => 'Export';

  @override
  String get exportSuccess => 'Export successful';

  @override
  String get deleteSuccess => 'Delete successful';

  @override
  String get send => 'Send';

  @override
  String get fail => 'fail';

  @override
  String get success => 'success';

  @override
  String get emptyData => 'Empty Data';

  @override
  String get requestSuccess => 'Request successful';

  @override
  String get add => 'Add';

  @override
  String get all => 'All';

  @override
  String get modify => 'Modify';

  @override
  String get responseType => 'Response Type';

  @override
  String get request => 'Request';

  @override
  String get response => 'Response';

  @override
  String get statusCode => 'Status code';

  @override
  String get done => 'Done';

  @override
  String get type => 'Type';

  @override
  String get enable => 'Enable';

  @override
  String get example => 'Example: ';

  @override
  String get responseHeader => 'Headers';

  @override
  String get requestHeader => 'Headers';

  @override
  String get requestLine => 'Request Line';

  @override
  String get requestMethod => 'Request Method';

  @override
  String get param => 'Param';

  @override
  String get replaceBodyWith => 'Replace Body With:';

  @override
  String get redirectTo => 'Redirect To:';

  @override
  String get redirect => 'Redirect';

  @override
  String get cannotBeEmpty => 'Cannot be empty';

  @override
  String get requestRewriteList => 'Request Rewrite List';

  @override
  String get requestRewriteRule => 'Request Rewrite Rule';

  @override
  String get requestRewriteEnable => 'Enable Request Rewrite';

  @override
  String get action => 'Action';

  @override
  String get multiple => 'Multiple';

  @override
  String get edit => 'Edit';

  @override
  String get disabled => 'Disabled';

  @override
  String requestRewriteDeleteConfirm(Object size) {
    return 'Delete $size rule(s)?';
  }

  @override
  String get useGuide => 'Use Guide';

  @override
  String get pleaseEnter => 'Please Enter';

  @override
  String get click => 'Click';

  @override
  String get replace => 'Replace';

  @override
  String get clickEdit => 'Click Edit';

  @override
  String get refresh => 'Refresh';

  @override
  String get selectFile => 'Select file';

  @override
  String get match => 'Match';

  @override
  String get value => 'Value';

  @override
  String get matchRule => 'Match Rule';

  @override
  String get emptyMatchAll => 'Empty means match all';

  @override
  String get newBuilt => 'New';

  @override
  String get newFolder => 'New Folder';

  @override
  String get enableSelect => 'Enable Select';

  @override
  String get disableSelect => 'Disable Select';

  @override
  String get deleteSelect => 'Delete Select';

  @override
  String get testData => 'Test Data';

  @override
  String get noChangesDetected => 'No changes detected';

  @override
  String get enterMatchData => 'Enter the data to be matched';

  @override
  String get modifyRequestHeader => 'Modify Header';

  @override
  String get headerName => 'Header Name';

  @override
  String get headerValue => 'Header Value';

  @override
  String get deleteHeaderConfirm => 'Do you want to delete the request header?';

  @override
  String get sequence => 'All Requests';

  @override
  String get domainList => 'Domain List';

  @override
  String get domainWhitelist => 'Proxy Domain Whitelist';

  @override
  String get domainBlacklist => 'Proxy Domain Blacklist';

  @override
  String get domainFilter => 'Proxy Domain List';

  @override
  String get appWhitelist => 'App Whitelist';

  @override
  String get appWhitelistDescribe =>
      'Only proxy Apps on the whitelist. If the whitelist is enabled, the blacklist will be invalid';

  @override
  String get appBlacklist => 'App Blacklist';

  @override
  String get scanCode => 'Scan Code Connect';

  @override
  String get addBlacklist => 'Add Proxy Blacklist';

  @override
  String get addWhitelist => 'Add Proxy Whitelist';

  @override
  String get deleteWhitelist => 'Delete Proxy Whitelist';

  @override
  String domainListSubtitle(Object count, Object time) {
    return 'Last Request Time: $time,  Count: $count';
  }

  @override
  String get selectAction => 'Select action';

  @override
  String get copy => 'Copy';

  @override
  String get copyHost => 'Copy Host';

  @override
  String get copyUrl => 'Copy URL';

  @override
  String get copyRequestResponse => 'Copy Request and Response';

  @override
  String get copyCurl => 'Copy cURL';

  @override
  String get copyAsPythonRequests => 'Copy as Python Requests';

  @override
  String get delete => 'Delete';

  @override
  String get rename => 'Rename';

  @override
  String get repeat => 'Repeat';

  @override
  String get repeatAllRequests => 'Repeat All Requests';

  @override
  String get repeatDomainRequests => 'Repeat Domain Requests';

  @override
  String get customRepeat => 'Custom Repeat';

  @override
  String get repeatCount => 'Iterations';

  @override
  String get repeatInterval => 'Interval(ms)';

  @override
  String get repeatDelay => 'Delay(ms)';

  @override
  String get scheduleTime => 'Schedule Time';

  @override
  String get fixed => 'fixed';

  @override
  String get random => 'random';

  @override
  String get keepCustomSettings => 'Keep custom settings';

  @override
  String get editRequest => 'Edit and Request';

  @override
  String get reSendRequest => 'The request has been resent';

  @override
  String get viewExport => 'View Export';

  @override
  String get timeDesc => 'Descending by time';

  @override
  String get timeAsc => 'Ascending by time';

  @override
  String get search => 'Search';

  @override
  String get clearSearch => 'Clear Search';

  @override
  String get requestType => 'Request type';

  @override
  String get keyword => 'Keyword';

  @override
  String get keywordSearchScope => 'Keyword search scope: ';

  @override
  String get favorite => 'Favorite';

  @override
  String get deleteFavorite => 'Delete Favorite';

  @override
  String get emptyFavorite => 'Empty Favorite';

  @override
  String get deleteFavoriteSuccess => 'Favorite deleted';

  @override
  String get name => 'Name';

  @override
  String get historyRecord => 'History';

  @override
  String get historyCacheTime => 'Cache Time';

  @override
  String get historyManualSave => 'Manual Save';

  @override
  String historyDay(Object day) {
    return '$day days';
  }

  @override
  String get historyForever => 'Forever';

  @override
  String historyRecordTitle(Object length, Object name) {
    return '$name Records $length';
  }

  @override
  String get historyEmptyName => 'Name cannot be empty';

  @override
  String historySubtitle(Object requestLength, Object size) {
    return 'Records $requestLength  file $size';
  }

  @override
  String get historyUnSave => 'Current record is not saved';

  @override
  String get historyDeleteConfirm => 'Do you want to delete this history?';

  @override
  String get requestEdit => 'Request Editing';

  @override
  String get encode => 'Encode';

  @override
  String get requestBody => 'Request Body';

  @override
  String get responseBody => 'Response Body';

  @override
  String get requestRewrite => 'Request Rewrite';

  @override
  String get newWindow => 'New Window';

  @override
  String get httpRequest => 'HTTP Request';

  @override
  String get enabledHttps => 'Enable HTTPS Proxy';

  @override
  String get installRootCa => 'Install Certificate';

  @override
  String get installCaLocal => 'Install Certificate to Local-Machine';

  @override
  String get downloadRootCa => 'Download Certificate';

  @override
  String get downloadRootCaNote =>
      'Note: If you set the default browser to other than Safari, click this line to copy and paste the link to Safari browser';

  @override
  String get generateCA => 'Generate new root certificate';

  @override
  String get generateCADescribe =>
      'Are you sure you want to generate a new root certificate? If confirmed,\nYou need to reinstall and trust the new certificate';

  @override
  String get resetDefaultCA => 'Reset Default Root Certificate';

  @override
  String get resetDefaultCADescribe =>
      'Are you sure you want to reset to the default root certificate?\nProxyPin default root certificate is the same for all users.';

  @override
  String get exportCaP12 => 'Export Root Certificate(.p12)';

  @override
  String get importCaP12 => 'Import Root Certificate(.p12)';

  @override
  String get trustCa => 'Trust Certificate';

  @override
  String get profileDownload => 'Profile Download';

  @override
  String get exportCA => 'Export Root Certificate';

  @override
  String get exportPrivateKey => 'Export Private Key';

  @override
  String get install => 'Install';

  @override
  String get installCaDescribe => 'Install CA Setting > Profile Download > Install';

  @override
  String get trustCaDescribe => 'Trust CA Setting > General > About > Certificate Trust Setting';

  @override
  String get androidRoot => 'System Certificate (ROOT Device)';

  @override
  String get androidRootMagisk =>
      'Magisk module: \nAndroid ROOT devices can be used Magisk ProxyPinCA System Certificate Module, After installing and restarting the phone Check the system certificate to see if there is a ProxyPinCA certificate. If there is, it indicates that the certificate has been successfully installed。';

  @override
  String androidRootRename(Object name) {
    return 'If the module does not take effect, you can install the system root certificate according to the online tutorial, and name the root certificate $name';
  }

  @override
  String get androidRootCADownload => 'Download System Certificate(.0)';

  @override
  String get androidUserCA => 'User Certificate';

  @override
  String get androidUserCATips => 'Tips: Android7+ many apps will not trust user certificates';

  @override
  String get androidUserCAInstall =>
      'Open settings -> Security -> Encryption and credentials -> Install certificate -> CA certificate';

  @override
  String get androidUserXposed =>
      'It is recommended to use the Xposed module for packet capture (no need for ROOT), click to view wiki';

  @override
  String get configWifiProxy => 'Configure mobile Wi-Fi proxy';

  @override
  String get caInstallGuide => 'Certificate Installation Guide';

  @override
  String get caAndroidBrowser => 'Open Google Browser on Android devices：';

  @override
  String get caIosBrowser => 'Open Safari on iOS devices：';

  @override
  String get localIP => 'Local IP ';

  @override
  String get mobileScan => 'Configure Wi-Fi proxy or Scan with Mobile App';

  @override
  String get decode => 'Decode';

  @override
  String get encodeInput => 'Enter the content to be converted';

  @override
  String get encodeResult => 'Conversion Result';

  @override
  String get encodeFail => 'Encoding failed';

  @override
  String get decodeFail => 'Decoding failed';

  @override
  String get shareUrl => 'Share Request URL';

  @override
  String get shareCurl => 'Share cURL Request';

  @override
  String get shareRequestResponse => 'Share Request and Response';

  @override
  String get captureDetail => 'Capture Detail';

  @override
  String get proxyPinSoftware => 'ProxyPin Open source traffic capture software for all platforms';

  @override
  String get prompt => 'Prompt';

  @override
  String get curlSchemeRequest => 'If the curl format is recognized, should it be converted into an HTTP request?';

  @override
  String get appExitTips => 'Press again to exit the program';

  @override
  String get remoteConnectDisconnect => 'Check remote connection failed, disconnected';

  @override
  String get reconnect => 'Reconnect';

  @override
  String remoteConnected(Object os) {
    return 'Connected $os, traffic will be forwarded to $os';
  }

  @override
  String get remoteConnectForward => 'Remote connection, forwarding requests to other terminals';

  @override
  String get connectSuccess => 'Connect successful';

  @override
  String get connectedRemote => 'Connected to remote';

  @override
  String get connected => 'Connected';

  @override
  String get notConnected => 'Not connected';

  @override
  String get disconnect => 'Disconnect';

  @override
  String get ipLayerProxy => 'IP Layer Proxy(Beta)';

  @override
  String get ipLayerProxyDesc =>
      'IP layer proxy can capture Flutter app requests, currently not very stable, welcome to submit PR';

  @override
  String get inputAddress => 'Input Address';

  @override
  String get syncConfig => 'Sync configuration';

  @override
  String get pullConfigFail => 'Failed to pull configuration, please check the network connection';

  @override
  String get sync => 'Sync';

  @override
  String get invalidQRCode => 'Unrecognized QR code';

  @override
  String get remoteConnectFail =>
      'Connection failed，Please check if it is allowed on the same LAN and firewall, iOS needs to enable local network permissions';

  @override
  String get remoteConnectSuccessTips => 'Your phone needs to enable packet capture in order to capture requests';

  @override
  String get windowMode => 'Window Mode';

  @override
  String get windowModeSubTitle => 'Enabled Packet Capture, Enter the background, Display a small window';

  @override
  String get pipIcon => 'Window shortcut icon';

  @override
  String get pipIconDescribe => 'Show quick access to small window Icon';

  @override
  String get headerExpanded => 'Headers Expanded';

  @override
  String get headerExpandedSubtitle => 'Details page Headers is expanded by default';

  @override
  String get bottomNavigation => 'Bottom Navigation';

  @override
  String get bottomNavigationSubtitle => 'Bottom navigation bar is displayed, effective after restart';

  @override
  String get memoryCleanup => 'Memory Cleanup';

  @override
  String get memoryCleanupSubtitle =>
      'Automatically clean up requests on memory limit reached and keep 32 most recent after cleaning';

  @override
  String get unlimited => 'Unlimited';

  @override
  String get custom => 'Custom';

  @override
  String get externalProxyAuth => 'Proxy Auth (Optional)';

  @override
  String get externalProxyServer => 'Proxy Server';

  @override
  String get externalProxyConnectFailure => 'External Proxy Connect failure';

  @override
  String get externalProxyFailureConfirm =>
      'Access to all http will fail due to network connectivity issues，Do you want to continue setting up external proxies。';

  @override
  String get mobileDisplayPacketCapture => 'Mobile Display Packet Capture:';

  @override
  String proxyPortRepeat(Object port) {
    return 'Startup failed, please check the port number $port is occupied。';
  }

  @override
  String get reset => 'Reset';

  @override
  String get proxyIgnoreDomain => 'Proxy ignores domain';

  @override
  String get domainWhitelistDescribe =>
      'Only proxy domain names on the whitelist. If the whitelist is enabled, the blacklist will be invalid';

  @override
  String get domainBlacklistDescribe => 'Domain names on the blacklist will not be proxied';

  @override
  String get domain => 'Host';

  @override
  String get enableScript => 'Enable Script';

  @override
  String get scriptUseDescribe => 'Use JavaScript to modify requests and responses';

  @override
  String get scriptEdit => 'Edit script';

  @override
  String get scrollEnd => 'Scroll to End';

  @override
  String get logger => 'Log';

  @override
  String get material3 => 'Material 3 is the latest version of Google’s open-source design system';

  @override
  String get iosVpnBackgroundAudio =>
      'After turning on packet capture, exit to the background. In order to maintain the main UI thread for network communication, a silent audio playback will be enabled to keep the main thread running. Otherwise, it will only run in the background for 30 seconds. Do you agree to play audio in the background after turning on packet capture?';

  @override
  String get markRead => 'Mark as read';

  @override
  String get autoRead => 'Auto read';

  @override
  String get highlight => 'Highlight';

  @override
  String get blue => 'Blue';

  @override
  String get green => 'Green';

  @override
  String get yellow => 'Yellow';

  @override
  String get red => 'Red';

  @override
  String get pink => 'Pink';

  @override
  String get gray => 'Gray';

  @override
  String get underline => 'Underline';

  @override
  String get requestBlock => 'Request Block';

  @override
  String get other => 'Other';

  @override
  String get certHashName => 'CA Hash Name';

  @override
  String get regExp => 'RegExp';

  @override
  String get systemCertName => 'System Certificate Name';

  @override
  String get qrCode => 'QR Code';

  @override
  String get scanQrCode => 'Scan QR Code';

  @override
  String get generateQrCode => 'Generate';

  @override
  String get saveImage => 'Save Image';

  @override
  String get selectImage => 'Select Image';

  @override
  String get inputContent => 'Input Content';

  @override
  String get errorCorrectLevel => 'Error Correct';

  @override
  String get output => 'Output';

  @override
  String get timestamp => 'Timestamp';

  @override
  String get convert => 'Convert';

  @override
  String get time => 'DateTime';

  @override
  String get nowTimestamp => 'Now timestamp';

  @override
  String get hosts => 'Hosts';

  @override
  String get toAddress => 'To Address';

  @override
  String get encrypt => 'Encrypt';

  @override
  String get decrypt => 'Decrypt';

  @override
  String get cipher => 'Cipher';

  @override
  String get appUpdateCheckVersion => 'Check for Updates';

  @override
  String get appUpdateNotAvailableMsg => 'Already Using The Latest Version';

  @override
  String get appUpdateDialogTitle => 'Update Available';

  @override
  String get appUpdateUpdateMsg => 'A new version of ProxyPin is available. Would you like to update now?';

  @override
  String get appUpdateCurrentVersionLbl => 'Current Version';

  @override
  String get appUpdateNewVersionLbl => 'New Version';

  @override
  String get appUpdateUpdateNowBtnTxt => 'Update Now';

  @override
  String get appUpdateLaterBtnTxt => 'Later';

  @override
  String get appUpdateIgnoreBtnTxt => 'Ignore';
}
