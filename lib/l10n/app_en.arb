{"requests": "Requests", "favorites": "Favorites", "history": "History", "toolbox": "Toolbox", "me": "Me", "preference": "Preferences", "feedback": "<PERSON><PERSON><PERSON>", "about": "About", "filter": "Proxy Filter", "script": "<PERSON><PERSON><PERSON>", "share": "Share", "port": "Port: ", "proxy": "Proxy", "externalProxy": "External Proxy", "username": "Username", "password": "Password", "proxySetting": "Proxy Setting", "systemProxy": "Set as System Proxy", "enabledHTTP2": "Enable HTTP2", "serverNotStart": "Proxy server not started", "download": "Download", "start": "Start", "stop": "Stop", "clear": "Clear", "httpsProxy": "HTTPS Proxy", "setting": "Settings", "mobileConnect": "Mobile Connect", "connectRemote": "Connect Remote", "remoteDevice": "Remote Device", "remoteDeviceList": "Remote Device List", "myQRCode": "My QR Code", "theme": "Theme", "followSystem": "Follow System", "themeColor": "Theme Color", "themeLight": "Light", "themeDark": "Dark", "language": "Language", "autoStartup": "Auto Start Recording Traffic", "autoStartupDescribe": "Automatically start recording traffic when the program starts", "copied": "Copied to clipboard", "cancel": "Cancel", "close": "Close", "save": "Save", "confirm": "Confirm", "confirmTitle": "Confirm operation", "confirmContent": "Are you sure about this operation?", "addSuccess": "Successfully added", "saveSuccess": "Saved successfully", "operationSuccess": "Operation succeeded", "import": "Import", "importSuccess": "Import successful", "importFailed": "Import failed", "export": "Export", "exportSuccess": "Export successful", "deleteSuccess": "Delete successful", "send": "Send", "fail": "fail", "success": "success", "emptyData": "Empty Data", "requestSuccess": "Request successful", "add": "Add", "all": "All", "modify": "Modify", "responseType": "Response Type", "request": "Request", "response": "Response", "statusCode": "Status code", "done": "Done", "type": "Type", "enable": "Enable", "example": "Example: ", "responseHeader": "Headers", "requestHeader": "Headers", "requestLine": "Request Line", "requestMethod": "Request Method", "param": "Param", "replaceBodyWith": "Replace Body With:", "redirectTo": "Redirect To:", "redirect": "Redirect", "cannotBeEmpty": "Cannot be empty", "requestRewriteList": "Request Rewrite List", "requestRewriteRule": "Request Rewrite Rule", "requestRewriteEnable": "Enable Request Rewrite", "action": "Action", "multiple": "Multiple", "edit": "Edit", "disabled": "Disabled", "requestRewriteDeleteConfirm": "Delete {size} rule(s)?", "useGuide": "Use Guide", "pleaseEnter": "Please Enter", "click": "Click", "replace": "Replace", "clickEdit": "Click Edit", "refresh": "Refresh", "selectFile": "Select file", "match": "Match", "value": "Value", "matchRule": "Match Rule", "emptyMatchAll": "Empty means match all", "newBuilt": "New", "newFolder": "New Folder", "enableSelect": "Enable Select", "disableSelect": "Disable Select", "deleteSelect": "Delete Select", "testData": "Test Data", "noChangesDetected": "No changes detected", "enterMatchData": "Enter the data to be matched", "modifyRequestHeader": "Modify Header", "headerName": "Header Name", "headerValue": "Header Value", "deleteHeaderConfirm": "Do you want to delete the request header?", "sequence": "All Requests", "domainList": "Domain List", "domainWhitelist": "Proxy Domain Whitelist", "domainBlacklist": "Proxy Domain Blacklist", "domainFilter": "Proxy Domain List", "appWhitelist": "App Whitelist", "appWhitelistDescribe": "Only proxy Apps on the whitelist. If the whitelist is enabled, the blacklist will be invalid", "appBlacklist": "App Blacklist", "scanCode": "Scan Code Connect", "addBlacklist": "Add Proxy Blacklist", "addWhitelist": "Add Proxy Whitelist", "deleteWhitelist": "Delete Proxy Whitelist", "domainListSubtitle": "Last Request Time: {time},  Count: {count}", "selectAction": "Select action", "copy": "Copy", "copyHost": "Copy Host", "copyUrl": "Copy URL", "copyRequestResponse": "Copy Request and Response", "copyCurl": "Copy cURL", "copyAsPythonRequests": "<PERSON><PERSON> as Python Requests", "delete": "Delete", "rename": "<PERSON><PERSON>", "repeat": "Repeat", "repeatAllRequests": "Repeat All Requests", "repeatDomainRequests": "Repeat Domain Requests", "customRepeat": "Custom Repeat", "repeatCount": "Iterations", "repeatInterval": "Interval(ms)", "repeatDelay": "Delay(ms)", "scheduleTime": "Schedule Time", "fixed": "fixed", "random": "random", "keepCustomSettings": "Keep custom settings", "editRequest": "Edit and Request", "reSendRequest": "The request has been resent", "viewExport": "View Export", "timeDesc": "Descending by time", "timeAsc": "Ascending by time", "search": "Search", "clearSearch": "Clear Search", "requestType": "Request type", "keyword": "Keyword", "keywordSearchScope": "Keyword search scope: ", "favorite": "Favorite", "deleteFavorite": "Delete Favorite", "emptyFavorite": "Empty Favorite", "deleteFavoriteSuccess": "Favorite deleted", "name": "Name", "historyRecord": "History", "historyCacheTime": "<PERSON><PERSON>", "historyManualSave": "Manual Save", "historyDay": "{day} days", "historyForever": "Forever", "historyRecordTitle": "{name} Records {length}", "historyEmptyName": "Name cannot be empty", "historySubtitle": "Records {requestLength}  file {size}", "historyUnSave": "Current record is not saved", "historyDeleteConfirm": "Do you want to delete this history?", "requestEdit": "Request Editing", "encode": "Encode", "requestBody": "Request Body", "responseBody": "Response Body", "requestRewrite": "Request Rewrite", "newWindow": "New Window", "httpRequest": "HTTP Request", "enabledHttps": "Enable HTTPS Proxy", "installRootCa": "Install Certificate", "installCaLocal": "Install Certificate to Local-Machine", "downloadRootCa": "Download Certificate", "downloadRootCaNote": "Note: If you set the default browser to other than Safari, click this line to copy and paste the link to Safari browser", "generateCA": "Generate new root certificate", "generateCADescribe": "Are you sure you want to generate a new root certificate? If confirmed,\nYou need to reinstall and trust the new certificate", "resetDefaultCA": "Reset Default Root Certificate", "resetDefaultCADescribe": "Are you sure you want to reset to the default root certificate?\nProxyPin default root certificate is the same for all users.", "exportCaP12": "Export Root Certificate(.p12)", "importCaP12": "Import Root Certificate(.p12)", "trustCa": "Trust Certificate", "profileDownload": "Profile Download", "exportCA": "Export Root Certificate", "exportPrivateKey": "Export Private Key", "install": "Install", "installCaDescribe": "Install CA Setting > Profile Download > Install", "trustCaDescribe": "Trust CA Setting > General > About > Certificate Trust Setting", "androidRoot": "System Certificate (ROOT Device)", "androidRootMagisk": "Magisk module: \nAndroid ROOT devices can be used Magisk ProxyPinCA System Certificate Module, After installing and restarting the phone Check the system certificate to see if there is a ProxyPinCA certificate. If there is, it indicates that the certificate has been successfully installed。", "androidRootRename": "If the module does not take effect, you can install the system root certificate according to the online tutorial, and name the root certificate {name}", "androidRootCADownload": "Download System Certificate(.0)", "androidUserCA": "User Certificate", "androidUserCATips": "Tips: Android7+ many apps will not trust user certificates", "androidUserCAInstall": "Open settings -> Security -> Encryption and credentials -> Install certificate -> CA certificate", "androidUserXposed": "It is recommended to use the Xposed module for packet capture (no need for ROOT), click to view wiki", "configWifiProxy": "Configure mobile Wi-Fi proxy", "caInstallGuide": "Certificate Installation Guide", "caAndroidBrowser": "Open Google Browser on Android devices：", "caIosBrowser": "Open Safari on iOS devices：", "localIP": "Local IP ", "mobileScan": "Configure Wi-Fi proxy or Scan with Mobile App", "decode": "Decode", "encodeInput": "Enter the content to be converted", "encodeResult": "Conversion Result", "encodeFail": "Encoding failed", "decodeFail": "Decoding failed", "shareUrl": "Share Request URL", "shareCurl": "Share cURL Request", "shareRequestResponse": "Share Request and Response", "captureDetail": "Capture Detail", "proxyPinSoftware": "ProxyPin Open source traffic capture software for all platforms", "prompt": "Prompt", "curlSchemeRequest": "If the curl format is recognized, should it be converted into an HTTP request?", "appExitTips": "Press again to exit the program", "remoteConnectDisconnect": "Check remote connection failed, disconnected", "reconnect": "Reconnect", "remoteConnected": "Connected {os}, traffic will be forwarded to {os}", "remoteConnectForward": "Remote connection, forwarding requests to other terminals", "connectSuccess": "Connect successful", "connectedRemote": "Connected to remote", "connected": "Connected", "notConnected": "Not connected", "disconnect": "Disconnect", "ipLayerProxy": "IP Layer Proxy(Beta)", "ipLayerProxyDesc": "IP layer proxy can capture Flutter app requests, currently not very stable, welcome to submit PR", "inputAddress": "Input Address", "syncConfig": "Sync configuration", "pullConfigFail": "Failed to pull configuration, please check the network connection", "sync": "Sync", "invalidQRCode": "Unrecognized QR code", "remoteConnectFail": "Connection failed，Please check if it is allowed on the same LAN and firewall, iOS needs to enable local network permissions", "remoteConnectSuccessTips": "Your phone needs to enable packet capture in order to capture requests", "windowMode": "Window Mode", "windowModeSubTitle": "Enabled Packet Capture, Enter the background, Display a small window", "pipIcon": "Window shortcut icon", "pipIconDescribe": "Show quick access to small window Icon", "headerExpanded": "Headers Expanded", "headerExpandedSubtitle": "Details page Headers is expanded by default", "bottomNavigation": "Bottom Navigation", "bottomNavigationSubtitle": "Bottom navigation bar is displayed, effective after restart", "memoryCleanup": "Memory Cleanup", "memoryCleanupSubtitle": "Automatically clean up requests on memory limit reached and keep 32 most recent after cleaning", "unlimited": "Unlimited", "custom": "Custom", "externalProxyAuth": "Proxy <PERSON> (Optional)", "externalProxyServer": "Proxy Server", "externalProxyConnectFailure": "External Proxy Connect failure", "externalProxyFailureConfirm": "Access to all http will fail due to network connectivity issues，Do you want to continue setting up external proxies。", "mobileDisplayPacketCapture": "Mobile Display Packet Capture:", "proxyPortRepeat": "Startup failed, please check the port number {port} is occupied。", "reset": "Reset", "proxyIgnoreDomain": "Proxy ignores domain", "domainWhitelistDescribe": "Only proxy domain names on the whitelist. If the whitelist is enabled, the blacklist will be invalid", "domainBlacklistDescribe": "Domain names on the blacklist will not be proxied", "domain": "Host", "enableScript": "Enable <PERSON>", "scriptUseDescribe": "Use JavaScript to modify requests and responses", "scriptEdit": "Edit script", "scrollEnd": "Scroll to End", "logger": "Log", "material3": "Material 3 is the latest version of Google’s open-source design system", "iosVpnBackgroundAudio": "After turning on packet capture, exit to the background. In order to maintain the main UI thread for network communication, a silent audio playback will be enabled to keep the main thread running. Otherwise, it will only run in the background for 30 seconds. Do you agree to play audio in the background after turning on packet capture?", "markRead": "<PERSON> as read", "autoRead": "Auto read", "highlight": "Highlight", "blue": "Blue", "green": "Green", "yellow": "Yellow", "red": "Red", "pink": "Pink", "gray": "<PERSON>", "underline": "Underline", "requestBlock": "Request Block", "other": "Other", "certHashName": "CA Hash Name", "regExp": "RegExp", "systemCertName": "System Certificate Name", "qrCode": "QR Code", "scanQrCode": "Scan QR Code", "generateQrCode": "Generate", "saveImage": "Save Image", "selectImage": "Select Image", "inputContent": "Input Content", "errorCorrectLevel": "<PERSON><PERSON><PERSON>", "output": "Output", "timestamp": "Timestamp", "convert": "Convert", "time": "DateTime", "nowTimestamp": "Now timestamp", "hosts": "Hosts", "toAddress": "To Address", "encrypt": "Encrypt", "decrypt": "Decrypt", "cipher": "Cipher", "appUpdateCheckVersion": "Check for Updates", "appUpdateNotAvailableMsg": "Already Using The Latest Version", "appUpdateDialogTitle": "Update Available", "appUpdateUpdateMsg": "A new version of ProxyPin is available. Would you like to update now?", "appUpdateCurrentVersionLbl": "Current Version", "appUpdateNewVersionLbl": "New Version", "appUpdateUpdateNowBtnTxt": "Update Now", "appUpdateLaterBtnTxt": "Later", "appUpdateIgnoreBtnTxt": "Ignore"}