{"requests": "抓包", "favorites": "收藏", "history": "历史", "toolbox": "工具箱", "me": "我的", "preference": "偏好设置", "feedback": "反馈", "about": "关于", "filter": "代理过滤", "script": "脚本", "share": "分享", "port": "端口号: ", "proxy": "代理", "externalProxy": "外部代理设置", "username": "用户名", "password": "密码", "proxySetting": "代理设置", "systemProxy": "设置为系统代理", "enabledHTTP2": "启用HTTP2", "serverNotStart": "未开启抓包", "download": "下载", "start": "开始", "stop": "停止", "clear": "清空", "httpsProxy": "HTTPS 代理", "setting": "设置", "mobileConnect": "手机连接", "connectRemote": "连接终端", "remoteDevice": "远程设备", "remoteDeviceList": "远程设备列表", "myQRCode": "我的二维码", "theme": "主题", "themeColor": "主题颜色", "followSystem": "跟随系统", "themeLight": "浅色", "themeDark": "深色", "language": "语言", "autoStartup": "自动开启抓包", "autoStartupDescribe": "程序启动时自动开始记录流量", "copied": "已复制到剪贴板", "cancel": "取消", "close": "关闭", "save": "保存", "confirm": "确认", "confirmTitle": "确认操作", "confirmContent": "是否确认此操作?", "addSuccess": "添加成功", "saveSuccess": "保存成功", "operationSuccess": "操作成功", "import": "导入", "importSuccess": "导入成功", "importFailed": "导入失败", "export": "导出", "exportSuccess": "导出成功", "deleteSuccess": "删除成功", "send": "发送", "fail": "失败", "success": "成功", "emptyData": "无数据", "requestSuccess": "请求成功", "add": "添加", "all": "全部", "modify": "修改", "responseType": "响应类型", "request": "请求", "response": "响应", "statusCode": "状态码", "done": "完成", "type": "类型", "enable": "启用", "example": "示例: ", "responseHeader": "响应头", "requestHeader": "请求头", "requestLine": "请求行", "requestMethod": "请求方法", "param": "参数", "replaceBodyWith": "消息体替换为:", "redirectTo": "重定向到:", "redirect": "重定向", "cannotBeEmpty": "不能为空", "requestRewriteList": "请求重写列表", "requestRewriteRule": "请求重写规则", "requestRewriteEnable": "是否启用请求重写", "action": "行为", "multiple": "多选", "edit": "编辑", "disabled": "禁用", "requestRewriteDeleteConfirm": "是否删除{size}条规则?", "useGuide": "使用文档", "pleaseEnter": "请输入", "click": "点击", "replace": "替换", "clickEdit": "点击编辑", "refresh": "刷新", "selectFile": "选择文件", "match": "匹配", "value": "值", "matchRule": "匹配规则", "emptyMatchAll": "为空表示匹配全部", "newBuilt": "新建", "newFolder": "新建文件夹", "enableSelect": "启用选择", "disableSelect": "禁用选择", "deleteSelect": "删除选择", "testData": "测试数据", "noChangesDetected": "未检测到变更", "enterMatchData": "输入待匹配的数据", "modifyRequestHeader": "修改请求头", "headerName": "请求头名称", "headerValue": "请求头值", "deleteHeaderConfirm": "是否删除该请求头", "sequence": "全部请求", "domainList": "域名列表", "domainWhitelist": "代理域名白名单", "domainBlacklist": "代理域名黑名单", "appWhitelist": "应用白名单", "appWhitelistDescribe": "只代理白名单中的应用, 白名单启用黑名单将会失效", "appBlacklist": "应用黑名单", "domainFilter": "域名代理列表", "scanCode": "扫码连接", "addBlacklist": "添加代理黑名单", "addWhitelist": "添加代理白名单", "deleteWhitelist": "删除代理白名单", "domainListSubtitle": "最后请求时间: {time},  次数: {count}", "selectAction": "选择操作", "copy": "复制", "copyHost": "复制域名", "copyUrl": "复制URL", "copyRequestResponse": "复制 请求和响应", "copyCurl": "复制 cURL", "copyAsPythonRequests": "复制 Python Requests", "delete": "删除", "rename": "重命名", "repeat": "重放", "repeatAllRequests": "重放所有请求", "repeatDomainRequests": "重放域名下请求", "customRepeat": "高级重放", "repeatCount": "次数", "repeatInterval": "间隔(ms)", "repeatDelay": "延时(ms)", "scheduleTime": "指定时间", "fixed": "固定", "random": "随机", "keepCustomSettings": "保持自定义设置", "editRequest": "编辑请求", "reSendRequest": "已重新发送请求", "viewExport": "视图导出", "timeDesc": "按时间降序", "timeAsc": "按时间升序", "search": "搜索", "clearSearch": "清除搜索", "requestType": "请求类型", "keyword": "关键词", "keywordSearchScope": "关键词搜索范围: ", "favorite": "收藏", "deleteFavorite": "删除收藏", "emptyFavorite": "暂无收藏", "deleteFavoriteSuccess": "已删除收藏", "name": "名称", "historyRecord": "历史记录", "historyManualSave": "手动保存", "historyDay": "{day}天", "historyForever": "永久", "historyCacheTime": "缓存时间", "historyEmptyName": "名称不能为空", "historyRecordTitle": "{name} 记录数 {length}", "historySubtitle": "记录数 {requestLength}  文件 {size}", "historyUnSave": "当前会话记录未保存", "historyDeleteConfirm": "是否删除该历史记录？", "requestEdit": "请求编辑", "encode": "编码", "decode": "解码", "requestBody": "请求体", "responseBody": "响应体", "requestRewrite": "请求重写", "newWindow": "新窗口打开", "httpRequest": "HTTP请求", "enabledHttps": "启用HTTPS代理", "installRootCa": "安装根证书", "installCaLocal": "安装根证书到本机", "downloadRootCa": "下载根证书", "downloadRootCaNote": "注意：如果您将默认浏览器设置为 Safari 以外的浏览器，请单击此行复制并粘贴 Safari 浏览器的链接", "generateCA": "重新生成根证书", "generateCADescribe": "您确定要生成新的根证书吗? 如果确认，\n则需要重新安装并信任新的证书", "resetDefaultCA": "重置默认根证书", "resetDefaultCADescribe": "确定要重置为默认根证书吗? ProxyPin默认\n根证书对所有用户都是相同的.", "exportCaP12": "导出根证书 (.p12)", "importCaP12": "导入根证书 (.p12)", "trustCa": "信任证书", "exportCA": "导出根证书", "exportPrivateKey": "导出私钥", "profileDownload": "已下载描述文件", "install": "安装", "installCaDescribe": "安装证书 设置 > 已下载描述文件 > 安装", "trustCaDescribe": "信任证书 设置 > 通用 > 关于本机 > 证书信任设置", "androidRoot": "系统证书 (ROOT设备)", "androidRootMagisk": "Magisk模块: \n安卓ROOT设备可以使用Magisk ProxyPinCA系统证书模块, 安装完重启手机后 在系统证书查看是否有ProxyPinCA证书，如果有说明证书安装成功。", "androidRootRename": "模块不生效可以根据网上教程安装系统根证书, 根证书命名成 {name}", "androidUserCA": "用户证书", "androidUserCATips": "提示：Android7+ 很多软件不会信任用户证书", "androidRootCADownload": "下载系统根证书(.0)", "androidUserCAInstall": "打开设置 -> 安全 -> 加密和凭据 -> 安装证书 -> CA 证书", "androidUserXposed": "推荐使用Xposed模块抓包(无需ROOT), 点击查看wiki", "configWifiProxy": "配置手机Wi-Fi代理", "caInstallGuide": "证书安装指南", "caAndroidBrowser": "在 Android 设备上打开浏览器访问：", "caIosBrowser": "在 iOS 设备上打开 Safari访问：", "localIP": "本地IP ", "mobileScan": "配置Wi-Fi代理或使用手机版扫描二维码", "encodeInput": "输入要转换的内容", "encodeResult": "转换结果", "encodeFail": "编码失败", "decodeFail": "解码失败", "shareUrl": "分享请求链接", "shareCurl": "分享 cURL 请求", "shareRequestResponse": "分享请求和响应", "captureDetail": "抓包详情", "proxyPinSoftware": "ProxyPin全平台开源抓包软件", "prompt": "提示", "curlSchemeRequest": "识别到curl格式，是否转换为HTTP请求？", "appExitTips": "再按一次退出程序", "remoteConnectDisconnect": "检查远程连接失败，已断开", "reconnect": "重新连接", "remoteConnected": "已连接{os}，流量将转发到{os}", "remoteConnectForward": "远程连接，将其他设备流量转发到当前设备", "connectSuccess": "连接成功", "connectedRemote": "已连接远程", "connected": "已连接", "notConnected": "未连接", "inputAddress": "输入地址", "disconnect": "断开连接", "ipLayerProxy": "IP层代理(Beta)", "ipLayerProxyDesc": "IP层代理可抓取Flutter应用请求，目前不是很稳定,欢迎提交PR", "syncConfig": "同步配置", "pullConfigFail": "拉取配置失败, 请检查网络连接", "sync": "同步", "invalidQRCode": "无法识别的二维码", "remoteConnectFail": "连接失败，请检查是否在同一局域网和防火墙是否允许, ios需要开启本地网络权限", "remoteConnectSuccessTips": "手机需要开启抓包才可以抓取请求哦", "windowMode": "窗口模式", "windowModeSubTitle": "开启抓包后 如果应用退回到后台，显示一个小窗口", "pipIcon": "窗口快捷图标", "pipIconDescribe": "展示快捷进入小窗口Icon", "headerExpanded": "Headers自动展开", "headerExpandedSubtitle": "详情页Headers栏是否自动展开", "bottomNavigation": "底部导航", "bottomNavigationSubtitle": "底部导航栏是否显示，重启后生效", "memoryCleanup": "内存清理", "memoryCleanupSubtitle": "到内存限制自动清理请求，清理后保留最近32条请求", "unlimited": "无限制", "custom": "自定义", "externalProxyAuth": "代理认证 (可选)", "externalProxyServer": "代理服务器", "externalProxyConnectFailure": "外部代理连接失败", "externalProxyFailureConfirm": "网络不通所有接口将会访问失败，是否继续设置外部代理。", "mobileDisplayPacketCapture": "手机端是否展示抓包:", "proxyPortRepeat": "启动失败，请检查端口号{port}是否被占用", "reset": "重置", "proxyIgnoreDomain": "代理忽略域名", "domainWhitelistDescribe": "只代理白名单中的域名, 白名单启用黑名单将会失效", "domainBlacklistDescribe": "黑名单中的域名不会代理", "domain": "域名", "enableScript": "启用脚本工具", "scriptUseDescribe": "使用 JavaScript 修改请求和响应", "scriptEdit": "编辑脚本", "scrollEnd": "跟踪滚动", "logger": "日志", "material3": "Material3是谷歌开源设计系统的最新版本", "iosVpnBackgroundAudio": "开启抓包后，退出到后台。为了维护主UI线程的网络通信，将启用静音音频播放以保持主线程运行。否则，它将只在后台运行30秒。您同意在启用抓包后在后台播放音频吗?", "markRead": "标记已读", "autoRead": "自动已读", "highlight": "高亮", "blue": "蓝色", "green": "绿色", "yellow": "黄色", "red": "红色", "pink": "粉色", "gray": "灰色", "underline": "下划线", "requestBlock": "请求屏蔽", "other": "其他", "certHashName": "证书Hash名称", "systemCertName": "系统证书名称", "regExp": "正则表达式", "qrCode": "二维码", "generateQrCode": "生成二维码", "scanQrCode": "扫描二维码", "saveImage": "保存图片", "selectImage": "选择图片", "inputContent": "输入内容", "errorCorrectLevel": "纠错等级", "output": "输出", "timestamp": "时间戳", "convert": "转换", "time": "时间", "nowTimestamp": "当前时间戳(秒)", "hosts": "Hosts 映射", "toAddress": "映射地址", "encrypt": "加密", "decrypt": "解密", "cipher": "加解密", "appUpdateCheckVersion": "检查更新", "appUpdateNotAvailableMsg": "已是最新版本", "appUpdateDialogTitle": "有可用更新", "appUpdateUpdateMsg": "ProxyPin 的新版本现已推出。您想现在更新吗？", "appUpdateCurrentVersionLbl": "当前版本", "appUpdateNewVersionLbl": "新版本", "appUpdateUpdateNowBtnTxt": "现在更新", "appUpdateLaterBtnTxt": "以后再说", "appUpdateIgnoreBtnTxt": "忽略"}