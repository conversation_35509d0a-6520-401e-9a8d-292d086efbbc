allprojects {
    repositories {
        google()
        mavenCentral()
    }

    subprojects {
        afterEvaluate { project ->
            if (project.hasProperty('android')) {
                project.android {
                    if (namespace == null) {
                        namespace project.group
                    }
                }
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    afterEvaluate { project ->
        if (project.extensions.findByName("android") != null) {
            Integer pluginCompileSdk = project.android.compileSdk
            if (pluginCompileSdk != null && pluginCompileSdk < 31) {
                project.logger.error(
                        "Warning: Overriding compileSdk version in Flutter plugin: "
                                + project.name
                                + " from "
                                + pluginCompileSdk
                                + " to 31 (to work around https://issuetracker.google.com/issues/199180389)."
                                + "\nIf there is not a new version of " + project.name + ", consider filing an issue against "
                                + project.name
                                + " to increase their compileSdk to the latest (otherwise try updating to the latest version)."
                )
                project.android {
                    compileSdk 31
                }
            }
        }
    }
}

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
