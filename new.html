<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8">
    <script>!function(t){var e=t._plt=t._plt||[];e.push(["tm","ps",+new Date]),(t.requestAnimationFrame||t.webkitRequestAnimationFrame||t.setTimeout)(function(){e.push(["tm","fraf",+new Date])})}(window);!function(){if("PerformanceLongTaskTiming" in window){var g=window.__tti={e:[]};g.o=new PerformanceObserver(function(l){g.e=g.e.concat(l.getEntries())});g.o.observe({entryTypes:["longtask"]})}}();!function(){var e,t,n,i,r={passive:!0,capture:!0},a=new Date,o=function(){i=[],t=-1,e=null,f(addEventListener)},c=function(i,r){e||(e=r,t=i,n=new Date,f(removeEventListener),u())},u=function(){if(t>=0&&t<n-a){var r={entryType:"first-input",name:e.type,target:e.target,cancelable:e.cancelable,startTime:e.timeStamp,processingStart:e.timeStamp+t};i.forEach((function(e){e(r)})),i=[]}},s=function(e){if(e.cancelable){var t=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?function(e,t){var n=function(){c(e,t),a()},i=function(){a()},a=function(){removeEventListener("pointerup",n,r),removeEventListener("pointercancel",i,r)};addEventListener("pointerup",n,r),addEventListener("pointercancel",i,r)}(t,e):c(t,e)}},f=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach((function(t){return e(t,s,r)}))},p="hidden"===document.visibilityState?0:1/0;addEventListener("visibilitychange",(function e(t){"hidden"===document.visibilityState&&(p=t.timeStamp,removeEventListener("visibilitychange",e,!0))}),!0);o(),self.webVitals={firstInputPolyfill:function(e){i.push(e),u()},resetFirstInputPolyfill:o,get firstHiddenTime(){return p}}}();</script>
    <link rel="dns-prefetch" href="//static.pddpic.com">
    <link rel="dns-prefetch" href="//api.pinduoduo.com">
    <link rel="dns-prefetch" href="//funimg.pddpic.com">
    <link rel="dns-prefetch" href="//t00img.yangkeduo.com">
    <link rel="dns-prefetch" href="//t16img.yangkeduo.com">
    <link rel="dns-prefetch" href="//avatar.yangkeduo.com">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="Cache-Control" content="no-cache,no-store,must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta property="og:title" content="拼多多商城">
    <meta property="og:description" content="风靡全国的拼团商城，优质商品新鲜直供，快来一起拼多多吧">
    <meta property="og:image" content="https://funimg.pddpic.com/base/share_logo.jpg">
    <title>拼多多</title>
    <script>window.__ENABLE_ALERT_WECHAT_LOGIN__=true;</script>
    <script>!function(){var e=0,t=!1,r=!1,n=window.__EXT_AUTOREPAIR_HOST_MAP__||{"static.pddpic.com":["static-1.pddpic.com","static-2.pddpic.com"]};function o(e,t){setTimeout((function(){var r=new Error;throw r.extraInfo={error_message:e,errorCode:527,type:501,url:t},r}))}window.addEventListener("error",(function(a){var i=a.target||{},c=i.src||i.href,d=(i.nodeName||"").toLowerCase(),s=document.createElement("a");s.href=c;var l=s.host,f=String(i.onerror),p=f.indexOf("Loading chunk")>0&&f.indexOf("failed")>0||!(!i.dataset||!i.dataset.webpack),u=f.indexOf("CSS_CHUNK_LOAD_FAILED")>0;if(["link","script"].indexOf(d)>=0&&["preload","prefetch"].indexOf(i.rel)<0&&!p&&!u&&n[l]){var y="script"===d,m=Date.now()%2,_=0===m?1:0;!function t(a){var i=document.createElement(d);i.dataset.retryFlag="_retry_".concat(a?1:2);var s=c.replace(l,n[l][a?m:_]);y?(i.src=s,i.crossOrigin="anonymous"):(i.href=s,i.rel="stylesheet"),i.onerror=function(){a?t(!1):y&&(!function(){if(3===(e+=1)){var t=document.createElement("div"),r=window.screen.width/375;document.body.appendChild(t),t.style.position="fixed",t.style.top="45%",t.style.left="50%",t.style.webkitTransform="translateX(-50%)",t.style.transform="translateX(-50%)",t.style.padding="".concat(14*r,"px ").concat(16*r,"px"),t.style.fontSize="".concat(15*r,"px"),t.style.background="rgba(0, 0, 0, .8)",t.style.borderRadius="".concat(8*r,"px"),t.style.color="#fff",t.style.textAlign="center",t.style.whiteSpace="nowrap",t.style.zIndex="9999",t.innerText="资源加载异常, 请切换网络重试",setTimeout((function(){t.remove()}),4e3)}}(),r||(r=!0,o("some_retry_fail",s)))},(y?document.body:document.head).appendChild(i)}(!0),y&&!t&&(t=!0,o("trigger_retry",c))}return null}),!0)}();</script>
    <script>var pinbridge=function(e){var n,t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};n=function(){try{if(top.pinbridge)return top.pinbridge}catch(e){}var e=0,n={},r={},i=window._fastJsN?function(e,n,t,i){var o="function"==typeof window._fastJsN.callNative&&window._fastJsN.callNative(e,n,t?JSON.stringify(t):null,i);return(o=o?JSON.parse(o):null)&&o.hasOwnProperty("errorCode")&&(r.callback(o.callID,o.errorCode,o.data),r.removeCallback(o.callID)),o}:function(e,n,t,r){!function(e,n){if(window&&window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.hybridAPIMessageHandler)window.webkit.messageHandlers.hybridAPIMessageHandler.postMessage({url:e});else{if(null!=(navigator.userAgent||navigator.vendor||window.opera).match(/phh_android_version/i))return alert(e);var t=document.createElement("iframe");t.setAttribute("src",e),t.setAttribute("style","display:none;"),t.setAttribute("height","0px"),t.setAttribute("width","0px"),t.setAttribute("frameborder","0"),document.body.appendChild(t),t.parentNode.removeChild(t),t=null}}("pinbridge:///request?t="+e+"&m="+n+"&p="+encodeURIComponent(JSON.stringify(t))+"&c="+r)};return r={check:function(e,n,t,i){e&&n||t&&t(!1),r.callNative("AMBridgeAPI","check",{module_name:e,method_name:n},function(e){t&&t(e&&e.exist)},function(){i?i():t&&t(!1)})},callback:function(e,t,r){r=r||{};var i=n[e];i&&(i.func?i.func(t,r):0===t&&i.onSuccess?i.onSuccess(r):0!==t&&i.onError&&i.onError(r,t))},callNative:function(r,o,a,c,l){a=a||{};var d=++e;return n[d]={onSuccess:c,onError:l},a=function r(i){if("object"!==(void 0===i?"undefined":t(i)))return i;var o={};for(var a in"[object Array]"===Object.prototype.toString.call(i)&&(o=[]),i)if(i.hasOwnProperty(a)){var c=i[a];if("function"==typeof c){var l=++e;n[l]={func:c},o[a]="__aimi_function_"+l}else"object"===(void 0===c?"undefined":t(c))&&null!==c?o[a]=r(c):o[a]=c}return o}(a),i(r,o,a,d)},callbackFromNative:function(e,n,t){r.callback(e,n,t)},removeCallback:function(e){delete n[e]},decodeFunctions:function(e){if("object"===(void 0===e?"undefined":t(e))){var n=function(n){if(!e.hasOwnProperty(n))return"continue";var i,o,a,c=e[n];if("string"==typeof c&&0===c.indexOf("__aimi_function_")){var l=c.substring("__aimi_function_".length).split("_");3===l.length&&(i=parseInt(l[0]),o=parseInt(l[1]),a=parseInt(l[2]),isNaN(i)||isNaN(o)||isNaN(a)||(e[n]=function(e,n){r.callNative("AMBridgeAPI","callback",{bridge_id:o,context_id:a,call_id:i,error_code:e,response:n})}))}else"object"===(void 0===c?"undefined":t(c))&&r.decodeFunctions(c)};for(var i in e)n(i)}}}}(),window.pinbridge=n;var r=n,i=r.callbackFromNative,o=r.callNative,a=r.check,c=r.callback,l=r.decodeFunctions,d=r.removeCallback,u=n;return e.callNative=o,e.callback=c,e.callbackFromNative=i,e.check=a,e.decodeFunctions=l,e.default=u,e.removeCallback=d,e}({});</script>
    <script>window.__ERROR_FILTER_LIST__ = {"filterList":[{"fields":{"errorMsg":"Illegal invocation"}},{"fields":{"errorMsg":"Error call native .*:.* cost over 1000ms","errorCode":"555555"}},{"fields":{"errorMsg":"initShareAndMenu 调用超时"}},{"fields":{"errorMsg":"Cannot read|set property '.*?' of null|undefined","platform":"unknown"}},{"fields":{"errorMsg":"ReferenceError: Can't find variable: WebViewJavascriptBridge","platform":"unknown"}},{"fields":{"errorMsg":"undefined is not an object \\(evaluating 'this.pages"}},{"fields":{"errorMsg":"^Cannot read properties of null|undefined \\(reading 'length|-1'\\)$"}},{"fields":{"errorMsg":"^Cannot convert undefined or null to object.*inject-with-store"}},{"fields":{"errorMsg":"Cannot convert a Symbol value to a string"}},{"fields":{"errorMsg":"Cannot.*?redefine.*?property.*?navigator"}},{"fields":{"errorMsg":"“TouchEvent”未定义","platform":"unknown"}},{"fields":{"errorMsg":"DevToolsAPI|WeixinJSBridge is not defined"}},{"fields":{"errorMsg":"渲染异常","errorCode":921,"platform":"unknown"}},{"fields":{"errorMsg":"无法重新定义不可配置的属性.*platform.*"}},{"fields":{"page":"Object Not Found Matching Id"}},{"fields":{"errorMsg":"^Request aborted$"}},{"fields":{"errorMsg":"^Network Error$"}},{"fields":{"errorMsg":"^Script error.?$","platform":"unknown"}},{"fields":{"errorMsg":"callNative AMAlert showAlert error: 60000|60004","errorCode":"60000|60004"}},{"fields":{"errorMsg":"config:fail:yf_zmq_udp_proxy jsapi yaaf_mpqq_websvr service error","platform":"qq"}},{"fields":{"errorMsg":"config:fail: no return  body","platform":"qq"}},{"fields":{"errorMsg":"unknown biz error"}},{"fields":{"errorMsg":"Request failed with status code 424|429"}},{"fields":{"errorMsg":"err_unknown_44104"}},{"fields":{"errorMsg":"系统繁忙，请稍后重试"}},{"fields":{"page":"Loading.* chunk .* failed"}},{"fields":{"platform":"unknown"},"eventType":500,"sampleRate":0.01},{"fields":{"platform":"^(?!unknown)"},"eventType":500,"sampleRate":0.1},{"fields":{"errorMsg":"html_error_img|lazy_image_error|cui_image_error"},"eventType":501,"sampleRate":0.01},{"fields":{"errorCode":"555555|555556","platform":"^(?!unknown)"},"eventType":501,"sampleRate":0.1},{"fields":{"platform":"unknown"},"eventType":501,"sampleRate":0.01},{"fields":{"errorMsg":"'TouchEvent' is not defined"}},{"fields":{"errorMsg":"goToAppGoodsDetailInWx"}},{"fields":{"page":"sku_order_render|sku_order_refresh","errorCode":"40001"}},{"fields":{"errorCode":"555560"},"sampleRate":0.1},{"fields":{"errorMsg":"native login modal error","errorCode":"60006"}},{"fields":{"errorMsg":"Use of const in strict mode"}},{"fields":{"errorMsg":"Unexpected token 'export'"}},{"fields":{"errorMsg":"window.webkit.messageHandlers"}},{"fields":{"errorMsg":"Cannot redefine property: connection","platform":"qq"}}],"replaceList":[{"fields":{"page":"assets-rcf"},"replaceErrorCode":700009},{"fields":{"platform":"unknown"},"replaceErrorCode":700000,"sampleRate":0.01},{"fields":{"errorMsg":"预渲染失败"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"callNative JSUIControl setRollingAlpha error: 60000"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"vedio_error_retry_3"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"Can't find variable: WeixinOpenTags"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"page":"sku_order_render|sku_order_refresh"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"面板显示.*?和摩根支付.*?不一致"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"paycheck_fail_undefined"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"唤起App失败"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"goto-app: launch_pdd_app_fail"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"{\"errMsg\":\"config"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"https:\u002F\u002Fgoo.gl\u002FLdLk22"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"WeixinJSBridge支付失败"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"微信sdk还没有初始化"},"replaceErrorCode":700001,"sampleRate":0.02},{"fields":{"errorMsg":"callNative .* error"},"replaceErrorCode":700002},{"fields":{"errorMsg":"^查询失败$"},"replaceErrorCode":700002},{"fields":{"errorMsg":"^Failed to fetch$"},"replaceErrorCode":700002},{"fields":{"page":"unhandledrejection reason:{\"error_stack\":\"\",\"Error\":{\"isTrusted\":true}}","platform":"qq"},"replaceErrorCode":700002},{"fields":{"errorMsg":"native request error"},"replaceErrorCode":700002},{"fields":{"errorMsg":"Uncaught Error.*领券失败"},"sampleRate":0.2,"replaceErrorCode":700002},{"fields":{"errorMsg":"Unexpected token {"},"replaceErrorCode":700002},{"fields":{"errorMsg":"system:access_denied"},"replaceErrorCode":700002},{"fields":{"errorMsg":"timeout after 5000ms"},"replaceErrorCode":700002},{"fields":{"errorMsg":"refreshAccessToken error"},"replaceErrorCode":700002},{"fields":{"errorMsg":"分享规则初始化失败"},"replaceErrorCode":700002},{"fields":{"errorMsg":"加载分享控制器失败"},"replaceErrorCode":700002},{"fields":{"errorMsg":"The operation was aborted"},"replaceErrorCode":700002},{"fields":{"errorMsg":"^批量领券失败$"},"replaceErrorCode":700003}]};</script>
    <script>var initInlineLogger=function(){function r(r,e){(null==e||e>r.length)&&(e=r.length);for(var n=0,t=Array(e);n<e;n++)t[n]=r[n];return t}function e(r,e){var n="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!n){if(Array.isArray(r)||(n=u(r))||e&&r&&"number"==typeof r.length){n&&(r=n);var t=0,o=function(){};return{s:o,n:function(){return t>=r.length?{done:!0}:{done:!1,value:r[t++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,d=!1;return{s:function(){n=n.call(r)},n:function(){var r=n.next();return a=r.done,r},e:function(r){d=!0,i=r},f:function(){try{a||null==n.return||n.return()}finally{if(d)throw i}}}}function n(r,e,n){return(e=function(r){var e=function(r,e){if("object"!=typeof r||!r)return r;var n=r[Symbol.toPrimitive];if(void 0!==n){var t=n.call(r,e||"default");if("object"!=typeof t)return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(r)}(r,"string");return"symbol"==typeof e?e:e+""}(e))in r?Object.defineProperty(r,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):r[e]=n,r}function t(r,e){var n=Object.keys(r);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(r);e&&(t=t.filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.push.apply(n,t)}return n}function o(r){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?t(Object(o),!0).forEach((function(e){n(r,e,o[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(o,e))}))}return r}function i(r,e){return function(r){if(Array.isArray(r))return r}(r)||function(r,e){var n=null==r?null:"undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(null!=n){var t,o,i,a,d=[],u=!0,s=!1;try{if(i=(n=n.call(r)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(t=i.call(n)).done)&&(d.push(t.value),d.length!==e);u=!0);}catch(r){s=!0,o=r}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return d}}(r,e)||u(r,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(e)||u(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(r){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},d(r)}function u(e,n){if(e){if("string"==typeof e)return r(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?r(e,n):void 0}}function s(r){return r.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent)}function c(r){var e=document.cookie?document.cookie.split("; "):[],n={};return e.some((function(e){var t=e.split("="),o=t.slice(1).join("="),i=s(t[0]);return o=s(o),n[i]=o,r===i})),r?n[r]||"":n}String.prototype.includes||(String.prototype.includes=function(r,e){return"number"!=typeof e&&(e=0),!(e+r.length>this.length)&&-1!==this.indexOf(r,e)}),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(r,e){if(null==this)throw new TypeError("this is null or not defined");var n=Object(this),t=n.length>>>0;if(0===t)return!1;var o,i,a=0|e,d=Math.max(a>=0?a:t-Math.abs(a),0);for(;d<t;){if((o=n[d])===(i=r)||"number"==typeof o&&"number"==typeof i&&isNaN(o)&&isNaN(i))return!0;d++}return!1}});var f=function(){for(var r,e=[],n=0;n<256;n++){r=n;for(var t=0;t<8;t++)r=1&r?3988292384^r>>>1:r>>>1;e[n]=r}return e}();function l(r){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;r=function(r){for(var e="",n=0;n<r.length;n++){var t=r.charCodeAt(n);t<128?e+=String.fromCharCode(t):t<2048?e+=String.fromCharCode(192|t>>6)+String.fromCharCode(128|63&t):t<55296||t>=57344?e+=String.fromCharCode(224|t>>12)+String.fromCharCode(128|t>>6&63)+String.fromCharCode(128|63&t):(t=65536+((1023&t)<<10|1023&r.charCodeAt(++n)),e+=String.fromCharCode(240|t>>18)+String.fromCharCode(128|t>>12&63)+String.fromCharCode(128|t>>6&63)+String.fromCharCode(128|63&t))}return e}(r),e^=-1;for(var n=0;n<r.length;n++)e=e>>>8^f[255&(e^r.charCodeAt(n))];return(-1^e)>>>0}var p=function(r){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,n=arguments.length>2?arguments[2]:void 0;if("function"!=typeof r||null!=n&&"function"!=typeof n)throw new TypeError("Expected a function");var t=[],o=[];return function(){for(var i=arguments.length,a=new Array(i),d=0;d<i;d++)a[d]=arguments[d];var u=n?n.apply(this,a):a[0],s=o.indexOf(u);if(s>-1)return t[s];var c=r.apply(this,a);return t.push(c),o.push(u),t.length>e&&(o.shift(),t.shift()),c}}((function(r){return(r="?"===r[0]?r.slice(1):r).split("&").reduce((function(r,e){if(e){var n=i(function(r,e){if(""===e)return[r];var n=r.indexOf(e);return-1===n?[r]:[r.slice(0,n),r.slice(n+e.length)]}(e,"="),2),t=n[0],o=n[1],a=void 0===o?"":o;try{r[decodeURIComponent(t)]=decodeURIComponent(a)}catch(r){d=r,console.error("node端不支持throwErrorAsync",d)}}var d;return r}),{})}));var v={MiniProgram:/miniprogram/i,WeChat:/MicroMessenger/i,QQ:/QQ\/[\d\.]+\s+/i,QQAndQzone:/(QQ\/[\d\.]+\s+)|Qzone/i,QQApp:/QQ\//i,Weibo:/Weibo/i,Ios:/iphone|ipad|ipod/i,IosVersion:/os (\d+)_(\d+)_?(\d+)?/i,Android:/Android/i,AndroidVersion:/Android (\d+).?(\d+)?.?(\d+)?/i,AndroidNative:/phh_android_version/i,IosNative:/phh_ios_version/i,IosApiRequest:/BundleID\/com.xunmeng.pinduoduo/i,Mobile:/Android|webOS|iPhone|iPad|iPod/i,AndroidNativeVersion:/(phh_android_version)\/([^\s]+)\s*/i,IosNativeVersion:/(phh_ios_version|AppVersion)\/([^\s]+)\s*/i,Harmony:/OpenHarmony/i,HarmonyVersion:/OpenHarmony (\d+).?(\d+)?.?(\d+)?/i,HarmonyNative:/phh_harmony_version/i,HarmonyNativeVersion:/(phh_harmony_version)\/([^\s]+)\s*/i,MecoWebViewCore:/MecoCore\/(\d)/i,MecoWebViewSdk:/MecoSDK\/(\d)/i},m="unknown",g="ios",h="android",y="tinyAndroid",_="wechat",w="weibo",b="qq",S="qqapp",A="wxapp",O="android",I="ios",C="harmony",R="unknown";function E(r,e){[r,e].forEach((function(r){if("string"!=typeof r)throw new TypeError("Invalid argument expected string")}));for(var n=function(r){return r.split(".").map((function(r){return parseInt(r,10)||0}))},t=n(r),o=n(e),i=Math.max(t.length,o.length),a=0;a<i;++a){var d=t[a]||0,u=o[a]||0;if(d!==u)return d-u}return 0}function M(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return v.MiniProgram.test(r)||"undefined"!=typeof window&&"miniprogram"===window.__wxjs_environment}function N(r){return v.Ios.test(r)?I:v.Android.test(r)?O:v.Harmony.test(r)?C:R}function j(){var r=function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return v.AndroidNative.test(r)?r.includes("app_type/tiny")?y:h:v.IosNative.test(r)||v.IosApiRequest.test(r)?g:v.WeChat.test(r)?M(r)?A:_:v.HarmonyNative.test(r)?y:v.Weibo.test(r)?w:v.QQApp.test(r)&&M(r)?S:v.QQAndQzone.test(r)?b:m}(navigator.userAgent),e=r===h,n=r===g,t=e||n,o=r===y,i=/phh_harmony_version/i.test(navigator.userAgent),a=t&&function(r,e){return function(r,e){var n=r.match(e);return n&&n[2]||""}(r,e===h?v.AndroidNativeVersion:v.IosNativeVersion)}(navigator.userAgent,r);return{isNativePlatform:t,isAndroid:e,isIos:n,isTinyNativePlatform:o,isHarmonyNativePlatform:i,platform:r,version:a}}var x="pdd_ban_report_domains",T="undefined"!=typeof window,P=!1,k=!1,V=!1,L=!1,Q={},q={},D={},F={th:"th",ta:"ta",pmm:"pmm",abtk:"abtk"},z=n(n(n({},F.th,["sandbox-tk.pdd.net"]),F.ta,["sandbox-tk.pdd.net"]),F.pmm,["apm.hutaojie.com"]),H=n(n(n(n({},F.th,["th-a","th-b","th"]),F.ta,["ta-a","ta"]),F.pmm,["apm-a","apm"]),F.abtk,["abtk"]);function J(){if(k)try{0===Object.keys(D).length?localStorage.removeItem(x):localStorage.setItem(x,JSON.stringify(D))}catch(r){console.error(r)}}function W(r,e){var n=Date.now();if(e){if(D[r]){var t=(D[r]||{}).errorFreq,o=void 0===t?0:t;o<=1?delete D[r]:D[r]={errorFreq:o-1,lastSuccTime:n},J()}}else{var i=(D[r]||{}).errorFreq,a=void 0===i?0:i;D[r]={errorFreq:Math.min(a+1,5),lastFailTime:n},J()}}function U(){if(!P&&T){V=-1!==location.hostname.indexOf("hutaojie");var r,e,n=j(),t=n.isNativePlatform,o=n.isIos,i=n.isTinyNativePlatform;if(L=t||i,V)Q=z;else{Q=JSON.parse(JSON.stringify(H)),o&&"loading"===document.readyState?(console.error("[uniReporter] ios without DOMContentLoaded, use default domains"),k=!1):(!function(){try{var r=localStorage.getItem(x);if(!r)return;D=JSON.parse(r)}catch(r){console.error(r)}}(),function(){try{var r=c("pdd_c_h5_report_domains");if(!r)return;var e=JSON.parse(r);Object.values(F).forEach((function(r){var n=e[r];n&&n instanceof Array&&n.length>=1&&(Q[r]=n,q[r]=!0)}))}catch(r){console.error(r)}}(),k=!0);var d=-1!==location.hostname.indexOf("yangkeduo.com")?"yangkeduo":"pinduoduo",u=(r=c().api_uid,e=void 0===r?"":r,Math.abs(function(r){for(var e=0,n=0;n<r.length;n++)e=(e<<5)-e+r.charCodeAt(n),e&=e;return e}(e.replace("==",""))));Object.values(F).forEach((function(r){var e=u%Q[r].length;Q[r]=[Q[r][e]].concat(a(Q[r].filter((function(r,n){return n!==e})))).map((function(e){return e+"."+([F.pmm,F.abtk].indexOf(r)>-1?"pinduoduo":d)+".com"}))}))}P=k}}function G(r,e,n){return new Promise((function(t,o){var i=0,a=function(){if(i>=e.length)return console.error("[uniReporter] all domains fail",e),void t(!1);var o=e[i];(function(e){return new Promise((function(n,t){var o=setTimeout((function(){t(new Error("[uniReporter] timeout 1200ms"))}),1200),i=function(r){clearTimeout(o),n(r)},a=function(r){clearTimeout(o),t(r)};if(L)window.pinbridge.callNative("JSNetwork","request",{method:"POST",url:e,headers:{"Content-Type":"text/plain;charset=UTF-8"},data:r},(function(r){i(r)}),(function(r,e){var n=new Error("[uniReporter] JSNetwork.request error: "+e);a(n)}));else{var d=new XMLHttpRequest;d.open("POST",e,!0),d.setRequestHeader("Content-Type","text/plain;charset=UTF-8"),d.withCredentials=!0,d.send(r),d.onload=function(){var r=d.status;r>=200&&r<300||413===r||512===r?i(d.responseText):a(d.statusText)},d.onerror=function(r){a(r)}}}))})(n(o)).then((function(){W(o,!0),t(!0)})).catch((function(r){W(o,!1),console.error("[uniReporter] xhr fail:",o,i,r),i++,a()}))};a()}))}function $(r){var e=a(Q[r]);return q[r]||(e=function(r){var e=!1;Object.keys(D).forEach((function(r){var n=D[r],t=n.lastFailTime,o=void 0===t?0:t,i=n.lastSuccTime,a=void 0===i?0:i;Math.max(o,a)<Date.now()-6e5&&(delete D[r],e=!0)})),e&&J(),r.sort((function(r,e){var n=D[r]||{},t=n.errorFreq,o=void 0===t?0:t,i=n.lastSuccTime,a=void 0===i?0:i,d=n.lastFailTime,u=void 0===d?0:d,s=D[e]||{},c=s.errorFreq,f=void 0===c?0:c,l=s.lastSuccTime,p=void 0===l?0:l,v=s.lastFailTime,m=void 0===v?0:v;return o!==f?o-f:a!==p?p-a:u!==m?u-m:0}));var n=r.filter((function(r){return!D[r]||D[r].errorFreq<5}));return 0===n.length?[r[0]]:n}(e)),e}function K(r,e,n){return P||U(),G(e,$(r),n)}function B(r){if(!r||!r.stack)return"";var e=r.stack.replace(/\\n/gi,"").split(/\bat\b/).slice(0,5).join("@").replace(/\?[^:]+/gi,""),n=r.toString();return e.indexOf(n)<0?n+"@"+e:e}T&&(window.$uniReport=K);var X,Z,Y=5,rr=501,er=502,nr=600,tr="undefined"==typeof window||(window.location.hostname.includes("m.hutaojie.com")||window.location.hostname.includes("panduoduo.yangkeduo.com")||["development","testing","panduoduo"].includes((X="prod_env","undefined"!=typeof window&&window.__ERROR_LOGGER_ENV__?(window.__ERROR_LOGGER_ENV__||{})[X]:""))||"http:"===window.location.protocol),or=void 0,ir={IosVersion:/(pddmt_ios_version)\/([^\s]+)\s*/i,AndroidVersion:/(pddmt_android_version)\/([^\s]+)\s*/i,MacVersion:/(pddMerchant_mac_version)\/([^\s]+)\s*/i};function ar(){if(void 0!==or)return or;var r=navigator.userAgent,e=[ir.IosVersion,ir.AndroidVersion,ir.MacVersion].find((function(e){return e.test(r)})),n=r.match(e);return or="",n&&(or=n[2]||""),or}var dr,ur,sr=function(r){if(Z)return Z;var e,t,o=tr?n(n(n(n({},h,"1"),g,"3"),y,"0"),"harmony","204"):n(n(n(n({},h,"3"),g,"5"),y,"59"),"harmony","334");return Z=o[r]||(e=navigator.userAgent,t="unkonwn",ir.IosVersion.test(e)?t="pddmt_ios":ir.AndroidVersion.test(e)?t="pddmt_android":ir.MacVersion.test(e)&&(t="pdd_merchant_mac"),(tr?n(n(n({},"pddmt_android","2"),"pddmt_ios","4"),"pdd_merchant_mac","171"):n(n(n({},"pddmt_android","4"),"pddmt_ios","6"),"pdd_merchant_mac","304"))[t])||"-1"},cr=(dr=function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0;if(n)return n;var t=window.location.pathname;if(r.indexOf(t)<0||e.length<=0)return t;var i,a=window.location.search,d=e.find((function(r){return a.indexOf(r)>-1}));if(d){var u=function(r){return o({},p(r))}(a);i=u[d]}return i?"/"===i[0]?i+".html":"/"+i+".html":t},ur=null,function(){if(!ur){for(var r=arguments.length,e=new Array(r),n=0;n<r;n++)e[n]=arguments[n];ur=dr.apply(null,e)}return ur});var fr,lr,pr={platform:"p",osVer:"osV",pddId:"pid",userId:"uid"};function vr(r){var n;if(!(window.__ERROR_SAMPLE_RATE__&&Math.random()>window.__ERROR_SAMPLE_RATE__||function(r){try{var n,t=r.common_tags,o=void 0===t?{}:t,i=r.datas[0],a=i.extras,d=void 0===a?{}:a,u=i.tags,s=void 0===u?{}:u,c=i.type,f=window.__ERROR_FILTER_LIST__||{},l=f.filterList,p=void 0===l?[]:l,v=f.replaceList,m=void 0===v?[]:v,g=function(r){var e=r.eventType,n=void 0===e?502:e,t=r.fields;return+c===+n&&Object.keys(t).every((function(r){var e=r,n=s;return pr[r]?(n=o,e=pr[r]):r.startsWith("common.")?(n=o,e=r.substring(7)):r.startsWith("payload.")&&(n=d,e=r.substring(8)),new RegExp(t[r]).test(n[e])}))},h=function(r,e){if(void 0!==r.sampleRate){var n="number"!=typeof r.sampleRate||r.sampleRate<0||r.sampleRate>1?1:r.sampleRate;return Math.random()>n}return e},y=e(p);try{for(y.s();!(n=y.n()).done;){var _=n.value;if(g(_))return h(_,!0)}}catch(r){y.e(r)}finally{y.f()}var w,b=e(m);try{for(b.s();!(w=b.n()).done;){var S=w.value;if(g(S)){var A=s.errorCode;return s.errorCode=""+S.replaceErrorCode,d.originErrorCode=A,h(S,!1)}}}catch(r){b.e(r)}finally{b.f()}}catch(r){}return!1}(r))){var t=null==r||null===(n=r.datas[0])||void 0===n?void 0:n.type,o=JSON.stringify(r);K(F.pmm,o,(function(r){return function(r,e){return"https://"+(tr?"apm.hutaojie.com":e)+(r===nr?"/api/pmm/front_log":"/api/pmm/front_err")}(t,r)})).catch((function(r){return console.error("@pdd/error-logger uniReport error",r)}))}}function mr(r){var e,n=r||{},t=n.bizSide,o=n.appId,i=n.testAppId,a=n.app_version,d=function(r){var e=r.bizSide,n=r.appId,t=r.testAppId;if("undefined"!=typeof window){var o={bizSide:"consumer-platform-fe"};return e&&(o.bizSide=e),tr&&!t?(console.error("缺少 testAppId 测试环境应用 id"),o):n?(o.appId=tr?t:n,o):(console.error("缺少 appId 正式环境环境应用 id"),o)}return{}}({bizSide:t,appId:o,testAppId:i}),u=Date.now(),s=Math.pow(10,5)+Math.floor(Math.random()*(Math.pow(10,6)-Math.pow(10,5)));return fr=void 0!==fr?fr:Number(null===(e=a||"")||void 0===e?void 0:e.split(".").reduce((function(r,e,n){return 0===n?r+e:""+r+(e.length<2?"0"+e:e)}),"")),{biz_side:d.bizSide,app:d.appId,level:2,version:fr,report_time_ms:u,rand_num:s,crc32:l(u+"-"+s)}}function gr(r,e){var n=sr(r);lr=lr||c("ETag")||c("api_uid");var t=function(r){var e=N(r),n="";if(e===I&&(n=v.IosVersion),e===O&&(n=v.AndroidVersion),e===C&&(n=v.HarmonyVersion),n){var t=r.match(n);return(t?[t[1],t[2],t[3]].map((function(r){return r?parseInt(r,10):0})):[]).join(".")}return""}(navigator.userAgent);return{p:r,runningPlatform:r,runningAppId:n,b:"",did:"",mid:"",osV:N(navigator.userAgent)+"_"+t,pid:lr,uid:e?e+"":""}}return function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=r.bizSide,n=r.appId,t=r.testAppId;e&&n&&t||console.error("initInlineLogger 缺少必传项 bizSize, appId, testAppId");var i=function(r){var e=r||{},n=e.message,t=void 0===n?"":n,i=e.filename,a=e.lineno,d=e.colno,u=e.error,c=function(r){var e={};if(r&&r.extraInfo){var n=r.extraInfo,t=n.errorMsg,o=n.error_msg,i=n.errorMessage,a=n.error_message,d=n.errorCode,u=n.error_code,s=n.page,c=n.type,f=n.url,l=n.payload;e.errorMsg=t||o||i||a,e.errorCode=d||u,e.page=s,e.type=c,e.url=f,e.payload=l,Object.keys(e).forEach((function(r){return void 0===e[r]&&delete e[r]}))}return e}(u);if(-1!==t.toLowerCase().indexOf("syntaxerror")){f(t+", "+i+", "+a+", "+d+", "+u,!1,c,B(u))}else{var l=(u||{}).message;s(o({errorMsg:"html_error_js: "+(t||l||"unknown"),page:"errMsg: "+l+", stack: "+B(u),errorCode:555558},c))}},a=function(r){var e,n,t,o={error_msg:(e=r.reason)&&e.message,error_stack:B(e),Error:e};s({errorMsg:"html_error_unhandledrejection: "+(o&&o.error_msg||"unknown"),page:"unhandledrejection reason:"+((n=JSON.stringify(o,(t=[],function(r,e){if(null!==e&&"object"===d(e)){if(t.indexOf(e)>=0)return;t.push(e)}return e})))&&n.replace(/(PDDAccessToken=|AccessToken=|"AccessToken":)\s*"?([^;,"]+)"?([;,]?\s*)/gi,(function(r,e,n,t){return""+e+n.length+t}))),payload:o&&o.extraInfo||{}})};window.__RESET_ERROR_LISTENER__=function(){window.removeEventListener("unhandledrejection",a),window.removeEventListener("error",i)},window.addEventListener("error",i),window.addEventListener("unhandledrejection",a),window.addEventListener("error",(function(r){if("[object Event]"===Object.prototype.toString.call(r)){var e=r.target;if(e){var n;if(-1!==["preload","prefetch"].indexOf(e.rel))return;var t=function(r){return r&&"string"==typeof r?r:""},o=t(e.src)||t(e.href)||t(e.getAttribute("href"));if(o&&o.indexOf&&"function"==typeof o.indexOf&&-1!==o.indexOf("127.0.0.1"))return;s({errorMsg:"html_error_"+e.nodeName.toLowerCase()+((null===(n=e.dataset)||void 0===n?void 0:n.retryFlag)||""),page:"Error: "+e.outerHTML,errorCode:555556,type:rr,url:o})}else f(JSON.stringify(r),!0)}}),!0);var u={wifi:1,"2g":2,"3g":3,"4g":4};function s(i){var a=i.errorMsg,d=i.errorCode,s=i.page,f=i.type,p=void 0===f?er:f,v=i.url,m=i.payload,g=void 0===m?{}:m,h=/(\w+).html/.exec(document.referrer),y=navigator.connection&&(navigator.connection.effectiveType||navigator.connection.type),_=j(),w=_.platform,b=_.version,S=_.isHarmonyNativePlatform?"harmony":w;!function(r){if(l)return r(l);var e="pdd_user_id";if(j().isNativePlatform&&window.pinbridge){var n=function(e){l=e.user_id||0,r(l)},t=function(){l=c(e),r(l)};return window.pinbridge.callNative("AMUser","info",{need_token:!1},n,t)}l=c(e),r(l)}((function(i){var c=Date.now()+"_"+i,f=mr({bizSide:e,appId:n,testAppId:t,app_version:b||ar()}),l=gr(S,i),m=function(){var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],t=e.universalPaths,i=e.universalQueryKeys,a=e.reportPath,d=r.app_version,u=r.log_version,s=void 0===u?"0.0.1":u,c=r.network,f=r.page,l=void 0===f?"":f,p=r.page_id,v=r.page_name,m=r.page_sn,g=r.page_url,h=r.refer_page_id,y=r.refer_page_name,_=r.refer_page_sn,w=r.user_agent,b=r.error_msg,S=r.error_message,A=r.serverIp,O=void 0===A?"":A,I=r.httpCode,C=void 0===I?"":I,R=r.httpMethod,E=void 0===R?"":R,M=r.errorCode,N=void 0===M?"555555":M,j=r.error_code,x=r.module,T=void 0===x?"":x,P=r.pageId,k=void 0===P?"":P,V=r.referPageId,L=void 0===V?"":V,Q=r.url,q=cr(t,i,a);return o({network:(c||"0")+"",serverIp:O,url:Q,errorCode:String(j||N),errorMsg:(b||S||"").substr(0,150),module:T,pageId:k,pagePath:q,pageSn:m?m+"":"",pageDomain:window.location.hostname,pageUrl:g||window.location.href,referPageId:L,page_id:p?p+"":"",refer_page_id:h?h+"":"",referPageName:y||"",referPageSn:_?_+"":"",pageName:v||"",page:l,log_version:s,user_agent:w||window.navigator.userAgent,app_version:d||""},n?{httpCode:String(C),httpMethod:E}:{})}({app_version:b,log_version:"1.0.0",network:u[y]||0,page:s,page_url:location.href,refer_page_name:h&&h[1],error_msg:a,url:v,errorCode:d},r);f.common_tags=l,f.datas=[{category:Y,type:p,timestamp:Date.now(),tags:m,extras:o({error_logger:"1",unique_tag:c},g)}],vr(f)}))}function f(r,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";!function(r,e){var n,t=j(),o=t.isNativePlatform,i=t.isAndroid,a=t.isIos,d=t.version;o&&(i&&E(d,"6.70.0")>=0||a&&E(d,"5.8.0")>=0)?null===(n=window.pinbridge)||void 0===n||n.callNative("JSRecovery","tryReload",{error_info:[r]},(function(){e("JSRecovery success")}),(function(r,n){e("JSRecovery fail: "+n)})):e("notNativeORLowVersion")}({type:"html_error",message:r},(function(i){s(o({errorMsg:"html_error: "+r,errorCode:555557,page:"capture: "+e+", result: "+i+", message: "+r+", extraMsg: "+t},n))}))}window.$inlineSendNormalLog=function(r){var e=r.errorMsg,n=r.errorCode,t=r.page,o=r.payload;s({errorMsg:e,errorCode:n,page:t,type:nr,payload:void 0===o?{}:o})};var l=0}}();</script>
    <script>initInlineLogger({"universalPaths":["/mall_quality_assurance.html","/comm_package_coupon.html"],"universalQueryKeys":["_t_timestamp"],"appId":"100601","testAppId":"100207","bizSide":"consumer-platform-fe","reportPath":"/goods.html"})</script>
    
            <link rel="preload" href="https://static.pddpic.com//assets/css/react_goods_e2876aca70a6fc855bc5.css" as="style" >
            
    
            <link rel="preload" href="https://static.pddpic.com//assets/js/react_pdd_d180e9c8f4ee079d23d0_1026.js" as="script" crossorigin="anonymous"><link rel="preload" href="https://static.pddpic.com//assets/js/vendor_24a41b3eedb8bc1d3c80_1026.js" as="script" crossorigin="anonymous"><link rel="preload" href="https://static.pddpic.com//assets/js/react_goods_67136ab241b1f94a73c8_1026.js" as="script" crossorigin="anonymous">
            
    
    
    
    </head>
    <body>
    

    <script>!function(n){(n.__pft=n.__pft||{}).bodyLoadTime=+new Date}(window);</script>
    <script>!function(t){var n=t._plt=t._plt||[];n.push(["tm","fp",+new Date]),document.addEventListener("DOMContentLoaded",function(){n.push(["tm","dr",+new Date])}),t.addEventListener("load",function(){n.push(["tm","ld",+new Date])})}(window);</script>
    
    <script>!function(n){(n.__pft=n.__pft||{}).inlineCssStart=+new Date}(window);</script>
    <style>body {
  -webkit-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
}

input,
textarea,
div[contentEditable=true],
p[contentEditable=true],
.u0Ve2ZAX {
  -webkit-user-select: text;
  -moz-user-select: text;
  -o-user-select: text;
  user-select: text;
}

button[data-active=red]:active,
div[data-active=red]:active,
a[data-active=red]:active,
p[data-active=red]:active {
  color: #edbbb8 !important;
  background-color: #c51e14 !important;
}

button[data-active=yellow]:active,
div[data-active=yellow]:active,
a[data-active=yellow]:active,
p[data-active=yellow]:active {
  color: #fce2bc !important;
  background-color: #f69e20 !important;
}

button[data-active=ghost-red]:active,
div[data-active=ghost-red]:active,
a[data-active=ghost-red]:active,
p[data-active=ghost-red]:active {
  color: #c51e14 !important;
  background-color: #fdf3f2 !important;
  border-color: #e02e24 !important;
}

button[data-active=ghost-gray]:active,
div[data-active=ghost-gray]:active,
a[data-active=ghost-gray]:active,
p[data-active=ghost-gray]:active {
  color: #878789 !important;
  background-color: #ebebeb !important;
  border-color: #8c8c8c !important;
}

button[data-active=cell-white]:active,
div[data-active=cell-white]:active,
a[data-active=cell-white]:active,
p[data-active=cell-white]:active {
  background-color: rgba(0,0,0,.08) !important;
}

button[data-active=cell-red]:active,
div[data-active=cell-red]:active,
a[data-active=cell-red]:active,
p[data-active=cell-red]:active {
  background-color: #f9d7d5 !important;
}

button[data-active=cell-yellow]:active,
div[data-active=cell-yellow]:active,
a[data-active=cell-yellow]:active,
p[data-active=cell-yellow]:active {
  background-color: #f9eda1 !important;
}

button[data-active=before-red]::before,
div[data-active=before-red]::before,
a[data-active=before-red]::before,
button[data-active=after-red]::after,
div[data-active=after-red]::after,
a[data-active=after-red]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: #000;
  pointer-events: none;
  -webkit-border-radius: inherit;
  border-radius: inherit;
}

button[data-active=before-red]:active,
div[data-active=before-red]:active,
a[data-active=before-red]:active {
  color: hsla(0,0%,100%,.6) !important;
}

button[data-active=before-red]:active::before,
div[data-active=before-red]:active::before,
a[data-active=before-red]:active::before {
  opacity: .15;
}

button[data-active=after-red]:active,
div[data-active=after-red]:active,
a[data-active=after-red]:active {
  color: hsla(0,0%,100%,.6) !important;
}

button[data-active=after-red]:active::after,
div[data-active=after-red]:active::after,
a[data-active=after-red]:active::after {
  opacity: .15;
}

button[data-active=before-white]::before,
div[data-active=before-white]::before,
a[data-active=before-white]::before,
button[data-active=after-white]::after,
div[data-active=after-white]::after,
a[data-active=after-white]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: #000;
  pointer-events: none;
  -webkit-border-radius: inherit;
  border-radius: inherit;
}

button[data-active=before-white]:active,
div[data-active=before-white]:active,
a[data-active=before-white]:active {
  color: rgba(0,0,0,.6) !important;
}

button[data-active=before-white]:active::before,
div[data-active=before-white]:active::before,
a[data-active=before-white]:active::before {
  opacity: .05;
}

button[data-active=after-white]:active,
div[data-active=after-white]:active,
a[data-active=after-white]:active {
  color: rgba(0,0,0,.6) !important;
}

button[data-active=after-white]:active::after,
div[data-active=after-white]:active::after,
a[data-active=after-white]:active::after {
  opacity: .05;
}

button[data-active=before-ghost]::before,
div[data-active=before-ghost]::before,
a[data-active=before-ghost]::before,
button[data-active=after-ghost]::after,
div[data-active=after-ghost]::after,
a[data-active=after-ghost]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  background: #e02e24;
  pointer-events: none;
  -webkit-border-radius: inherit;
  border-radius: inherit;
}

button[data-active=before-ghost]:active,
div[data-active=before-ghost]:active,
a[data-active=before-ghost]:active {
  color: rgba(234,46,36,.6) !important;
}

button[data-active=before-ghost]:active::before,
div[data-active=before-ghost]:active::before,
a[data-active=before-ghost]:active::before {
  opacity: .05;
}

button[data-active=after-ghost]:active,
div[data-active=after-ghost]:active,
a[data-active=after-ghost]:active {
  color: rgba(234,46,36,.6) !important;
}

button[data-active=after-ghost]:active::after,
div[data-active=after-ghost]:active::after,
a[data-active=after-ghost]:active::after {
  opacity: .05;
}

li[data-active=base-btn-red]:active,
button[data-active=base-btn-red]:active,
div[data-active=base-btn-red]:active,
a[data-active=base-btn-red]:active,
span[data-active=base-btn-red]:active {
  color: hsla(0,0%,100%,.7) !important;
  background-color: #c51e14 !important;
}

li[data-active=base-btn-white]:active,
button[data-active=base-btn-white]:active,
div[data-active=base-btn-white]:active,
a[data-active=base-btn-white]:active,
span[data-active=base-btn-white]:active {
  color: #878789 !important;
  background-color: #ebebeb !important;
  border: none !important;
}

li[data-active=base-btn-white]:active::after,
button[data-active=base-btn-white]:active::after,
div[data-active=base-btn-white]:active::after,
a[data-active=base-btn-white]:active::after,
span[data-active=base-btn-white]:active::after {
  content: "";
  position: absolute;
  width: 199%;
  height: 200%;
  border: 1px solid #8c8c8c;
  transform: scale3d(0.5, 0.5, 1);
  transform-origin: 0 0;
  pointer-events: none;
  border-radius: calc(4px*2);
  top: 0;
  left: 0;
}

@media only screen and (-webkit-device-pixel-ratio: 1.5) {
  li[data-active=base-btn-white]:active::after,
  button[data-active=base-btn-white]:active::after,
  div[data-active=base-btn-white]:active::after,
  a[data-active=base-btn-white]:active::after,
  span[data-active=base-btn-white]:active::after {
    transform: scale(0.5);
  }
}

li[data-active=base-btn-ghost]:active,
button[data-active=base-btn-ghost]:active,
div[data-active=base-btn-ghost]:active,
a[data-active=base-btn-ghost]:active,
span[data-active=base-btn-ghost]:active {
  color: #c51e14 !important;
  background-color: #fdf3f2 !important;
}

li[data-active=base-text-black]:active,
button[data-active=base-text-black]:active,
div[data-active=base-text-black]:active,
a[data-active=base-text-black]:active,
span[data-active=base-text-black]:active {
  color: #151516 !important;
}

li[data-active=base-text-red]:active,
button[data-active=base-text-red]:active,
div[data-active=base-text-red]:active,
a[data-active=base-text-red]:active,
span[data-active=base-text-red]:active {
  color: #c51e14 !important;
}

li[data-active=base-text-black-bg]:active,
button[data-active=base-text-black-bg]:active,
div[data-active=base-text-black-bg]:active,
a[data-active=base-text-black-bg]:active,
span[data-active=base-text-black-bg]:active {
  background-color: rgba(0,0,0,.08) !important;
}

@media only screen and (min-device-height: 812px) {
  #enVTXMXb {
    bottom: calc(0.76rem + constant(safe-area-inset-bottom));
    bottom: calc(0.76rem + env(safe-area-inset-bottom));
  }

  .X8M7HPvt {
    bottom: calc(-0.76rem + constant(safe-area-inset-bottom));
    bottom: calc(-0.76rem + env(safe-area-inset-bottom));
  }

  .OMtriLx0 {
    bottom: calc(0.76rem + constant(safe-area-inset-bottom));
    bottom: calc(0.76rem + env(safe-area-inset-bottom));
  }

  .RUhfhV_K {
    padding-bottom: calc(0px + constant(safe-area-inset-bottom));
    padding-bottom: calc(0px + env(safe-area-inset-bottom));
  }
}

#main {
  background-color: rgba(0,0,0,0);
}

.enable-select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -o-user-select: text;
  user-select: text;
}

.goods-container-v2 {
  position: relative;
  width: 100%;
  max-width: 768px;
  min-height: 100%;
  margin: 0 auto;
  padding: 0 0 .54rem 0;
  overflow-x: hidden;
}

.goods-container-v2.not-sale {
  background-color: #f4f4f4;
  min-height: 100vh;
}

.goods-container-v2 {
  padding-bottom: calc(0.54rem + constant(safe-area-inset-bottom));
  padding-bottom: calc(0.54rem + env(safe-area-inset-bottom));
}

.JOvk5Gur {
  background-color: #fff;
  overflow: hidden;
}

.GNrMaxlJ {
  background-color: #f4f4f4;
}

.cf3ThrcR {
  visibility: hidden;
}

.PqqOqss3 {
  background-color: #fff;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: scroll;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
} {
  /*! normalize.css v3.0.2 | MIT License | git.io/normalize */
}

@media only screen and (max-width: 480px) {
  html {
    font-size: 128px;
  }
}

@media only screen and (max-width: 414px) {
  html {
    font-size: 110.4px;
  }
}

@media only screen and (max-width: 412px) {
  html {
    font-size: 109.8666666667px;
  }
}

@media only screen and (max-width: 400px) {
  html {
    font-size: 106.6666666667px;
  }
}

@media only screen and (max-width: 393px) {
  html {
    font-size: 104.8px;
  }
}

@media only screen and (max-width: 375px) {
  html {
    font-size: 100px;
  }
}

@media only screen and (max-width: 360px) {
  html {
    font-size: 96px;
  }
}

@media only screen and (max-width: 345px) {
  html {
    font-size: 92px;
  }
}

@media only screen and (max-width: 320px) {
  html {
    font-size: 85.3333333333px;
  }
}

html {
  -webkit-text-size-adjust: none;
  font-size: 26.6666666667vw;
}

@media only screen and (min-width: 768px) {
  html {
    font-size: 204.8px;
  }
}

* {
  box-sizing: border-box;
  border: 0;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
  outline: 0;
  margin: 0;
  padding: 0;
}

*::before, *::after {
  box-sizing: border-box;
}

html {
  text-size-adjust: 100%;
  width: 100%;
  height: 100%;
  font-family: PingFang SC,Helvetica,STHeiti STXihei,Microsoft YaHei,Microsoft JhengHei,Arial;
}

body {
  margin: 0;
  width: 100%;
  height: 100%;
  font-family: PingFang SC,Helvetica,STHeiti STXihei,Microsoft YaHei,Microsoft JhengHei,Arial;
}

body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td {
  vertical-align: baseline;
  border: 0 none;
  list-style: outside none none;
}

i, em {
  font-style: normal;
}

ol, ul {
  list-style: none;
}

article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

audio, canvas, progress, video {
  display: inline-block;
  vertical-align: baseline;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden], template {
  display: none;
}

a {
  color: #333;
  background-color: rgba(0,0,0,0);
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

a:visited, a:active, a:hover {
  text-decoration: none;
  outline: 0;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b, strong {
  font-weight: bold;
}

dfn {
  font-style: italic;
}

h1 {
  font-size: 2em;
  margin: .67em 0;
}

mark {
  background: #ff0;
  color: #000;
}

small {
  font-size: 80%;
}

sub {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

img {
  border: 0;
  max-width: 100%;
  width: auto;
  height: auto;
  vertical-align: top;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 1em 40px;
}

hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}

pre {
  overflow: auto;
}

code, kbd, pre, samp {
  font-family: monospace,monospace;
  font-size: 1em;
}

button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
  resize: none;
}

button {
  overflow: visible;
  text-transform: none;
}

select {
  text-transform: none;
}

button, html input[type=button] {
  -webkit-appearance: button;
  cursor: pointer;
}

input[type=reset], input[type=submit] {
  -webkit-appearance: button;
  cursor: pointer;
}

button[disabled], html input[disabled] {
  cursor: default;
}

button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input {
  line-height: normal;
}

input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

input[type=checkbox], input[type=radio] {
  box-sizing: border-box;
  padding: 0;
}

input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
  height: auto;
}

input[type=search] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}

input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
}

fieldset {
  border: 1px solid silver;
  margin: 0 2px;
  padding: .35em .625em .75em;
}

legend {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
}

optgroup {
  font-weight: bold;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

td, th {
  padding: 0;
}

.icon {
  font-style: normal;
  font-smoothing: antialiased;
}

body {
  background: #f4f4f4;
  color: #666;
  font-size: 12px;
  line-height: 1.5;
  margin: auto;
  max-width: 768px;
  user-select: none;
}

.container {
  position: relative;
  width: 100%;
  max-width: 768px;
  min-height: 100%;
  margin: 0 auto;
  padding: 0 0 49px 0;
  background-color: #f4f4f4;
}

.pdd-go-to-app .icon {
  font-family: icomoon-comm;
  font-style: normal;
}

.font-center {
  font-family: PingFang SC,STHeiti STXihei,Microsoft YaHei,Microsoft JhengHei;
}

@font-face {
  font-family: "icomoon-comm";
  src: url("https://funimg.pddpic.com/fonts/comm_iconfont_20210701.ttf");
  font-weight: normal;
  font-style: normal;
  line-height: 1;
}

@font-face {
  font-family: "icomoon";
  src: url("https://funimg.pddpic.com/fonts/iconfont_20180706.ttf");
  font-weight: normal;
  font-style: normal;
  line-height: 1;
}.k0uDdSr_ {
  width: 100%;
  height: 2.15rem;
  padding-top: .51rem;
  text-align: center;
  margin-bottom: -0.08rem;
}

.k0uDdSr_ .WIBcW3Vc {
  font-size: .7rem;
  font-family: "icomoon-comm";
  color: #ccc;
  line-height: 1;
  margin-bottom: .2rem;
}

.k0uDdSr_ .i5X9aSEz {
  font-size: .16rem;
  line-height: .16rem;
  color: #58595b;
}

.k0uDdSr_.vWHAX2Bp {
  height: calc(2.15rem + constant(safe-area-inset-top));
  height: calc(2.15rem + env(safe-area-inset-top));
  padding-top: calc(0.51rem + constant(safe-area-inset-top));
  padding-top: calc(0.51rem + env(safe-area-inset-top));
}

.PdOB9B75 {
  width: 100%;
  height: 1.18rem;
  padding-top: .2rem;
  text-align: center;
  background-color: #fff;
}

.PdOB9B75 .WIBcW3Vc {
  font-size: .49rem;
  font-family: "icomoon-comm";
  color: #ccc;
  line-height: 1;
  margin-bottom: .1rem;
  position: relative;
  top: .01rem;
}

.PdOB9B75 .i5X9aSEz {
  font-size: .15rem;
  line-height: .15rem;
  color: #58595b;
}

.PdOB9B75.vWHAX2Bp {
  height: calc(1.18rem + constant(safe-area-inset-top));
  height: calc(1.18rem + env(safe-area-inset-top));
  padding-top: calc(0.2rem + constant(safe-area-inset-top));
  padding-top: calc(0.2rem + env(safe-area-inset-top));
}

.p6ZGJTb1 {
  padding: 0 0 .2rem;
  text-align: center;
  background-color: #fff;
}

.p6ZGJTb1 .WIBcW3Vc {
  font-size: .36rem;
  font-family: "icomoon-comm";
  color: #ccc;
  line-height: .8;
  margin-bottom: .1rem;
}

.p6ZGJTb1 .i5X9aSEz {
  font-size: .15rem;
  line-height: .15rem;
  color: #58595b;
  height: .15rem;
}

.p6ZGJTb1.vWHAX2Bp {
  padding-top: .16rem;
}

.p6ZGJTb1.vWHAX2Bp {
  padding-top: calc(0.16rem + constant(safe-area-inset-top));
  padding-top: calc(0.16rem + env(safe-area-inset-top));
}

.z4oJvvhT {
  font-size: .24rem;
  font-family: "icomoon-comm";
  color: #58595b;
  position: absolute;
  right: .14rem;
  top: .09rem;
  line-height: .24rem;
}

.yAyrgjpu {
  padding-top: .16rem;
  position: relative;
}.fBp2KoXx {
  width: 100%;
  background-color: #f4f4f4;
  height: .08rem;
}

.fBp2KoXx+.fBp2KoXx {
  height: 0 !important;
}

.HCtLhQc2 {
  background-color: #fff;
  display: inline-block;
  width: 100%;
}

.KlGVpw3u {
  background-color: #fff;
}

.N3evFt90 {
  padding-top: .02rem;
}

.N3evFt90::after {
  top: .15rem !important;
}

.Fh1J97nj {
  background: #fff;
  padding-bottom: .12rem;
}</style>
    <script>!function(n){(n.__pft=n.__pft||{}).inlineCssEnd=+new Date}(window);</script>
    
    <div id="main" class="container"><div class="goods-container-v2 not-sale"><div class="PdOB9B75"><div aria-hidden="true" class="WIBcW3Vc"></div><span class="i5X9aSEz">商品已售罄，推荐以下相似商品</span></div><div class="fBp2KoXx" style="background-color:#f4f4f4;height:0.08rem"></div><div style="height:10px"></div></div></div>

    <script>
        window.rawData={"store":{"errorRedirectUrl":"\u002F","isPre":false,"$likeMallText":"关注","$likeMallEntryTip":"关注的店铺可在个人中心的“店铺关注”入口查看","preRenderBannerSrc":null,"preRenderHideNoFirstScreenContent":false,"preRenderShowMock":false,"initDataObj":{"goods":{"serverTime":1753272633,"serverTimeTen":***********,"weakenGoodsName":0,"goodsID":************,"brandId":"","goodsType":1,"localGroups":[],"hasLocalGroup":0,"topGallery":[],"viewImageData":[],"detailGallery":[],"videoGallery":[],"descVideoGallery":[],"groupTypes":[],"skus":[],"thumbUrl":"","hdThumbUrl":"","eventType":0,"isApp":0,"isFreshmanApp":0,"eventComing":false,"isSpike":false,"isTodaySpike":false,"isTomorrowSpike":false,"isSpikeComing":false,"gpv":null,"quickRefund":false,"rv":false,"maxNormalPrice":"0","minNormalPrice":"0","maxGroupPrice":"0","minGroupPrice":"0","maxOnSaleGroupPrice":"0","minOnSaleGroupPrice":"0","maxOnSaleNormalPrice":"0","minOnSaleNormalPrice":"0","unselectNormalSavePrice":"0","skipGoodsIDs":[""],"tag":null,"icon":null,"tagIcon":[],"isColdGoods":0,"goodsProperty":[],"skuProperty":[],"linePrice":"0","priceStyle":0,"quicklyExpire":{},"goodsExpansionPrice":"0","beforeRemindSeconds":300,"bottomBanner":null,"isAppFlow":true,"groupNumFull":false,"promptExplain":"","isAbnormalStatus":true,"statusExplain":"商品已售罄，推荐以下相似商品","statusIcon":59326,"hideMall":false,"isMallRec":false,"status":5,"isGoodsOnSale":false,"isSkuOnSale":false,"isOnSale":false,"showRecTitle":1,"showRec":1,"eventTime":1753272633,"mallService":{"service":[],"vipService":{},"honorService":{}},"skuDirectOrder":false,"ui":{},"control":{"ai_assistant_enable":false,"enable_fold_fav_and_mall":false},"transmission":{},"uin":"DAXQW3JDHHRV5N6Q7TCWVFGJS4_GEXDA","isMobileWeChatPlatform":false,"contextData":{"isNativePlatform":false,"hostname":"mobile.yangkeduo.com","isMiniProgram":false},"DDType":false,"isDuoDuoSingleGroup":false,"isDuoDuoDoubleGroup":false,"isNewBottomTips":false,"isLowGroupNewBottomTips":false,"nowTime":1753272633,"nowTimeTen":***********,"isABTestGoodsPrerenderOC":true,"isAbDirectOrderRepayModifyPanel":true,"isAbDirectOrderMianmiV2":true,"isAbSwitchToWechatIfSignXyhfFailed":false,"hasAddressLine":false,"ignoreShowSkuSelector":false,"useBestPromotion":true,"usePayPromotion":true,"goodsImageQuantityGrey":true,"goodsImageQuantityParams":{"resizeWidth":1300,"quality":90},"isABGoodsRecOffset":false,"isABfastGroupBuyQa":false,"isABLongBannerMode":true,"isGoodsBottomAfterCouponRefresh":true,"isABBottomMallIconStyle":false,"isABWxGotoAppEnforce":false,"ifLiveCouponSkuSelector":true,"showGoodsLabel":true,"guideXyhfCountdown":false,"supportGbs":true,"balanceNotEnoughToMianMi":true,"payCanceledToXyhf":true,"applyDuoDuoPayCancelToMianMi":true,"enableSkuMask":true,"bottomBtnIndex":false,"canRefreshAfterOrder":false,"allowOcHosts":["mobile.pinduoduo.com"],"forwardOcHost":"mobile.yangkeduo.com","livingValidationUrl":"https:\u002F\u002Frenzheng.pinduoduo.com\u002Fliving-validation\u002Fliving-validation.html","useH5FaceCert":true,"bannerHeight":"3.75rem","isUseRawBackBannerUrl":false},"mall":null,"liveInfo":null,"queries":{"goods_id":"************","refer_page_sn":"10390","uin":"DAXQW3JDHHRV5N6Q7TCWVFGJS4_GEXDA","refer_page_name":"psnl_verification","refer_page_id":"10390_1753272578337_avjlrz3ct1"},"isIOS":false,"isSystemIOS":false,"guideConfig":{"waitTime":20000,"presentTime":5000},"userAgent":"Mozilla\u002F5.0 (Linux; Android 10; K) AppleWebKit\u002F537.36 (KHTML, like Gecko) Chrome\u002F********* Mobile Safari\u002F537.36","webp":true,"oakData":{"activityCollection":{},"ui":{},"goods":{},"control":{"aiAssistantEnable":false,"enableFoldFavAndMall":false},"promotion":{"events":{}}},"isABRafLoadJS":true,"isABChatActiveDuration":true,"isABRefreshToken":true,"referer":"https:\u002F\u002Fmobile.yangkeduo.com\u002Fgoods.html?goods_id=************&refer_page_sn=10390&uin=DAXQW3JDHHRV5N6Q7TCWVFGJS4_GEXDA&refer_page_name=psnl_verification&refer_page_id=10390_1753272578337_avjlrz3ct1"},"extra":{"mallActiveTimeText":""},"isNativePlatform":false,"isLiteNativePlatform":false,"isTinyNativePlatform":false,"categoryCouponInfo":{},"popupModalElement":null,"isIOS":false,"isWhiteFont":false,"isTransparentBar":false,"isShowFailingGroupHint":false,"isWeChatMiniProgram":false,"isShowFastGroupBuyTip":false,"skuOcExtend":{},"bannerState":{"currentIndex":0,"goToPageIndex":0,"hasShowedSimpleGoods":false,"simpleGoodsList":[]},"fakeAppDownloadShow":false,"withBtnScaleAnimation":false,"appCouponGray":0,"appDownloadCouponGray":0,"ifHigherMallInfo":false,"ifHasPushState":false,"abTestValues":{"pwc_cc4bc7":{"h5_button_enhance":{"vids_str":"1185601","_vid":1185601,"_vids":[1185601]},"sku":{"sku_height":"88%","vids_str":"1087742,1087765","_vid":1087742,"_vids":[1087742,1087765]},"alignWithNative":{"value":1,"vids_str":"1169079,1169084,1169086,1169088","_vid":1169079,"_vids":[1169079,1169084,1169086,1169088]},"h5_button_fitter":{"vids_str":"1199843,1199848,1199862,1199866,1199877,1199882","_vid":1199843,"_vids":[1199843,1199848,1199862,1199866,1199877,1199882]}}},"animationControllers":[],"animationGroupTimer":null,"mallDetailInfo":{},"viewCtx":{},"detailsIsInner":false,"funcAssemblySet":{},"$isSupportWebp":true,"isHarmonyBApp":false,"goodsCommentsInLayerGrey":false,"goodsSkuImageHotGrey":false,"goodsImageQuantityGrey":true,"goodsImageQuantityParams":{"resizeWidth":1300,"quality":90},"isDDMCMiniProgram":false,"isABGoodsStreamRender":true,"isServerRendered":true,"isFinishInitLoading":true,"isPreClientDataFetch":false}};
    </script>
    <script>
        window.leo={"data":{"goods_prerender_oc":"A","goods_prefetch_order_checkout":"600","goods_guide_wait_time":"20000","goods_guide_present_time":"5000","raf_load_js":"A","regions_new":"A","allow_oc_hosts":"[\"mobile.pinduoduo.com\"]","forward_oc_host":"mobile.yangkeduo.com","oc_comp_or_remote":"{\"ios\": \"remote\", \"android\": \"remote\"}","shoes_stream_render":"A","fast_group_buy_qa":"B","goods_rec_offset":"B","comm_toast_by_url_params":"1","goods_direct_order_repay_modify_panel":"1","goods_direct_order_mianmi_v2":"A","goods_direct_order_switch_wechat_if_sign_xyhf_failed":"A","address_line":"0","ignore_show_sku_selector":"0","force_app_by_url_params":"1","chat_active_duration":"A","enable_refresh_token":"A","long_banner_mode":"A","goods_bottom_after_coupon_refresh":"1","use_best_promotion":"A","use_pay_promotion":"1","bottom_mall_icon_style":"B","raw_back_banner_url":"B","wx_goto_app_enforce":"C","live_coupon_sku_selector":"B","comment_projector_back":"A","bottom_btn_index":"A","comment_img_size_opt":"B","goods_comments_layer":"B","goods_sku_image_hot":"B","goods_image_quantity":"A","goods_direct_order_goods_label":"A","sku_guide_xyhf_countdown":"0","living_validation_url":"https:\u002F\u002Frenzheng.pinduoduo.com\u002Fliving-validation\u002Fliving-validation.html","use_h5_face_cert":"1","goods_direct_order_gbs":"1","balance_not_enough_to_mianmi":"1","pay_canceled_to_xyhf":"1","apply_duoduo_pay_cancel_to_mianmi":"1","sku_mask":"A","can_refresh_after_order":"0"},"csrFallback":false,"fallbackData":{}};
    </script>
    <script>
        window.__webpack_public_path__="https://static.pddpic.com/";
    </script>
    
    <script>
        var pft = window.__pft = window.__pft || {};
        pft.nodePerf = {};
        pft.reqId = "";
        pft.isCSR = false;
        pft.isFallbackCSR = false;
    </script>
    
    <script>window.abTest={"pwc_cc4bc7":{"h5_button_enhance":{"vids_str":"1185601","_vid":1185601,"_vids":[1185601]},"sku":{"sku_height":"88%","vids_str":"1087742,1087765","_vid":1087742,"_vids":[1087742,1087765]},"alignWithNative":{"value":1,"vids_str":"1169079,1169084,1169086,1169088","_vid":1169079,"_vids":[1169079,1169084,1169086,1169088]},"h5_button_fitter":{"vids_str":"1199843,1199848,1199862,1199866,1199877,1199882","_vid":1199843,"_vids":[1199843,1199848,1199862,1199866,1199877,1199882]}}};</script>
    
        <script>
            window.__NAVIGATION_MAP__={"download.html":"matthew_download.html","market_download.html":"undefined.html","down_market_download.html":"undefined.html","comm_order_snapshot.html":"undefined.html","miff_transplantation_pretence.html":"refresh_slew_forlorn.html","group185.html":"group186.html","pjlkvgcf.html":"transac_virtual_card_pwd.html","ddplteec.html":"transac_virtual_card_pwd.html","svideo_personal.html":"fyxmkief.html?page_key=1","goods_express.html":"psnl_goods_help.html?_t_module_name=goods_express","search_view.html":"relative_goods.html?__rp_name=search_view"};
        </script>
        
    
        <script>
            window.__GLOBAL_LEO_CONFIG__={"universalLinkDomain":{"value":"app1.yangkeduo.com"}};
        </script>
        
    
        <script>
            window.__ERROR_SAMPLE_RATE__ = 1;
        </script>
        
    
        <script>
            window.__CMT_HOST__ = 'apm.pinduoduo.com';
        </script>
        
    
        <script>
            window.__CMT_AMPLIFY_RATE__ = 10;
        </script>
        
    
        <script>
            window.__CDN_IMG__={"useIpFallback":true,"retryLimit":3,"backupDomainConfigMap":{"dl.pddpic.com":{"dl-1.pddpic.com":50,"dl-2.pddpic.com":50},"t00img.yangkeduo.com":{"img-1.pddpic.com":30,"img-2.pddpic.com":30,"img-3.pddpic.com":20,"img-4.pddpic.com":20},"t13img.yangkeduo.com":{"promotion-1.pddpic.com":50,"promotion-2.pddpic.com":30,"promotion-3.pddpic.com":20},"t20img.yangkeduo.com":{"avatar2-1.pddpic.com":45,"avatar2-2.pddpic.com":45,"avatar2-3.pddpic.com":10},"t22img.yangkeduo.com":{"review-1.pddpic.com":50,"review-2.pddpic.com":50,"review-3.pddpic.com":0},"images.pinduoduo.com":{"images-1.pinduoduo.com":60,"images-2.pinduoduo.com":40},"img.pddpic.com":{"img-1.pddpic.com":30,"img-2.pddpic.com":30,"img-3.pddpic.com":20,"img-4.pddpic.com":20},"t04img.yangkeduo.com":{"t04img-b.yangkeduo.com":100},"commimg.pddpic.com":{"commimg-1.pddpic.com":50,"commimg-2.pddpic.com":50},"promotion.pddpic.com":{"promotion-1.pddpic.com":50,"promotion-2.pddpic.com":30,"promotion-3.pddpic.com":20},"funimg.pddpic.com":{"funimg-1.pddpic.com":50,"funimg-2.pddpic.com":50},"avatar.pddpic.com":{"avatar-b.pddpic.com":100},"avatar2.pddpic.com":{"avatar2-1.pddpic.com":50,"avatar2-2.pddpic.com":50},"avatar3.pddpic.com":{"avatar3-1.pddpic.com":50,"avatar3-2.pddpic.com":50},"review.pddpic.com":{"review-1.pddpic.com":50,"review-2.pddpic.com":50,"review-3.pddpic.com":0}}};
        </script>
        
    
            <link href="https://static.pddpic.com//assets/css/react_goods_e2876aca70a6fc855bc5.css" rel="stylesheet" >
            
    
    <script id="__LOADABLE_REQUIRED_CHUNKS__" type="application/json" crossorigin="anonymous">[]</script><script id="__LOADABLE_REQUIRED_CHUNKS___ext" type="application/json" crossorigin="anonymous">{"namedChunks":[]}</script>
    
            <script type="text/javascript" src="https://static.pddpic.com//assets/js/react_anti_co_7c53431a4571d3bd55ff_1026.js" crossorigin="anonymous"></script><script type="text/javascript" src="https://static.pddpic.com//assets/js/react_pdd_d180e9c8f4ee079d23d0_1026.js" crossorigin="anonymous"></script><script type="text/javascript" src="https://static.pddpic.com//assets/js/vendor_24a41b3eedb8bc1d3c80_1026.js" crossorigin="anonymous"></script><script type="text/javascript" src="https://static.pddpic.com//assets/js/react_goods_67136ab241b1f94a73c8_1026.js" crossorigin="anonymous"></script>
            
    
    
            <script>
                window.__SPEPKEY__ = 'pdd-web-commodity';
            </script>
            <script crossorigin="anonymous" src=https://static.pddpic.com/assets-rcf/b9216582_63786f6feb675e65b7e2bda71327fcfd.js ></script>
        
    
    
    
    <script>window;</script>
    </body>
</html>
    