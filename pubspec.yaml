name: proxypin
description: ProxyPin
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.1.9+19

environment:
  sdk: '>=3.0.2 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any
  cupertino_icons: ^1.0.8
  pointycastle: ^4.0.0
  logger: ^2.5.0
  date_format: ^2.0.9
  window_manager: ^0.5.0
  windows_single_instance: ^1.0.1
  desktop_multi_window:
    git:
      url: https://gitee.com/wanghongenpin/flutter-plugins.git
      path: packages/desktop_multi_window
  path_provider: ^2.1.5
  file_picker: ^10.2.0
  proxy_manager: ^0.0.3
  permission_handler: ^12.0.0+1
  flutter_toastr: ^1.0.3
  share_plus: ^11.0.0
  flutter_js:
    git:
      url: https://github.com/abner/flutter_js
      ref: master
  flutter_code_editor:
    git:
      url: https://github.com/wanghongenpin/flutter-code-editor.git
      ref: secure-keyboard
  flutter_desktop_context_menu: ^0.2.0
  device_info_plus: ^11.5.0
  shared_preferences: ^2.5.3
  image_pickers: ^2.0.6
  url_launcher: ^6.3.1
  toastification: ^3.0.2

  qr_flutter: ^4.1.0
  flutter_qr_reader_plus: ^1.0.6

  brotli: ^0.6.0
  zstandard: ^1.3.27

  win32audio: ^1.3.1
  vclibs: ^0.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

# The following section is specific to Flutter packages.
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/certs/ca.crt
    - assets/certs/ca_key.pem
    - assets/icon.png
    - assets/js/